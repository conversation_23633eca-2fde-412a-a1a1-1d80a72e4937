# Checklist: Refactor Capability Checklist to Status Document

**STATUS:** PENDING

This checklist outlines the process for refactoring the `capability_checklist.md` into a more comprehensive `capability-status.md`, incorporating toolset type and activation status from `settings.json`.

---

## 1. Setup & Validation
- [X] **1.1.** Read the `settings.json` file to identify all configured `mcpServers` and `internalTools`.
- [X] **1.2.** Read the existing `capability_checklist.md` to get the current list of tools and their test statuses.

## 2. Core Task Execution
- [X] **2.1.** Synthesize the data from both files into a new, structured format. For each tool, I will determine:
    - **Toolset:** The name of the toolset (e.g., `filesystem`, `neo4j-cypher`).
    - **Tool Name:** The specific tool name (e.g., `read_file`, `create_entities`).
    - **Type:** `MCP` or `Internal`.
    - **Status:** `Active` (if present in `settings.json`) or `Inactive`.
    - **Tested, Passed, Comments:** Migrated from the old checklist.
- [X] **2.2.** Generate a new Markdown document (`capability-status.md`) with a separate table for each toolset, reflecting the new structure.
- [X] **2.3.** Write the new content to a temporary file named `capability-status.md`.
- [X] **2.4.** Propose the deletion of the old `capability_checklist.md` file.
- [X] **2.5.** Upon approval, delete the old `capability_checklist.md` file.

## 3. Review & Verification
- [X] **3.1.** Read the new `capability-status.md` file to confirm its content and structure are correct.
- [X] **3.2.** List the directory contents to verify the old file is gone and the new one exists.

## 4. Cleanup & Archival
- [X] **4.1.** Propose the archival of this execution checklist.
- [X] **4.2.** Generate the `move` command for the user to execute.

---
## Logs
*   `2025-06-29 22:00:00 UTC` - Checklist created.
