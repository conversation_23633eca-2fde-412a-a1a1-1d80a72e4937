{"time":"2025-06-29 12:50:59.799+0000","level":"INFO","category":"o.n.g.f.m.GlobalModule","message":"Logging config in use: File '/var/lib/neo4j/conf/server-logs.xml'"}
{"time":"2025-06-29 12:50:59.963+0000","level":"WARN","category":"o.n.k.i.JvmChecker","message":"The max heap memory has not been configured. It is recommended that it is always explicitly configured, to ensure the system has a balanced configuration. Until then, a JVM computed heuristic of 8401190912 bytes is used instead. If you are running neo4j server, you need to configure server.memory.heap.max_size in neo4j.conf. If you are running neo4j embedded, you have to launch the JVM with -Xmx set to a value. You can run neo4j-admin server memory-recommendation for memory configuration suggestions."}
{"time":"2025-06-29 12:50:59.982+0000","level":"WARN","category":"o.n.k.i.Jvm<PERSON><PERSON><PERSON>","message":"The initial heap memory has not been configured. It is recommended that it is always explicitly configured, to ensure the system has a balanced configuration. Until then, a JVM computed heuristic of 528482304 bytes is used instead. If you are running neo4j server, you need to configure server.memory.heap.initial_size in neo4j.conf. If you are running neo4j embedded, you have to launch the JVM with -Xms set to a value. You can run neo4j-admin server memory-recommendation for memory configuration suggestions."}
{"time":"2025-06-29 12:51:00.341+0000","level":"INFO","category":"o.n.d.i.DefaultIdentityModule","message":"Generated new ServerId: ServerId{3ff82403} (3ff82403-37b9-4a25-81af-5b5b90192352)"}
{"time":"2025-06-29 12:51:00.343+0000","level":"INFO","category":"o.n.d.i.DefaultIdentityModule","message":"This instance is ServerId{3ff82403} (3ff82403-37b9-4a25-81af-5b5b90192352)"}
{"time":"2025-06-29 12:51:01.735+0000","level":"INFO","category":"o.n.s.CommunityNeoWebServer","message":"======== Neo4j 2025.05.1 ========"}
{"time":"2025-06-29 12:51:01.812+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\n                                                                ********************************************************************************\n                                                                                             [ System diagnostics ]                             \n                                                                ********************************************************************************"}
{"time":"2025-06-29 12:51:01.813+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\n                                                                --------------------------------------------------------------------------------\n                                                                                         [ System memory information ]                          \n                                                                --------------------------------------------------------------------------------\n                                                                Total Physical memory: 31.29GiB\n                                                                Free Physical memory: 26.73GiB\n                                                                Committed virtual memory: 11.60GiB\n                                                                Total swap space: 8.000GiB\n                                                                Free swap space: 8.000GiB\n                                                                "}
{"time":"2025-06-29 12:51:01.813+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\n                                                                --------------------------------------------------------------------------------\n                                                                                           [ JVM memory information ]                           \n                                                                --------------------------------------------------------------------------------\n                                                                Free  memory: 40.39MiB\n                                                                Total memory: 120.0MiB\n                                                                Max   memory: 7.824GiB\n                                                                Garbage Collector: G1 Young Generation: [G1 Eden Space, G1 Survivor Space, G1 Old Gen]\n                                                                Garbage Collector: G1 Concurrent GC: [G1 Old Gen]\n                                                                Garbage Collector: G1 Old Generation: [G1 Eden Space, G1 Survivor Space, G1 Old Gen]\n                                                                Memory Pool: CodeHeap 'non-nmethods' (Non-heap memory): committed=2.438MiB, used=1.582MiB, max=5.566MiB, threshold=0B\n                                                                Memory Pool: Metaspace (Non-heap memory): committed=48.38MiB, used=47.70MiB, max=-1B, threshold=0B\n                                                                Memory Pool: CodeHeap 'profiled nmethods' (Non-heap memory): committed=7.563MiB, used=7.313MiB, max=117.2MiB, threshold=0B\n                                                                Memory Pool: Compressed Class Space (Non-heap memory): committed=6.063MiB, used=5.785MiB, max=1.000GiB, threshold=0B\n                                                                Memory Pool: G1 Eden Space (Heap memory): committed=72.00MiB, used=48.00MiB, max=-1B, threshold=?\n                                                                Memory Pool: G1 Old Gen (Heap memory): committed=44.00MiB, used=28.73MiB, max=7.824GiB, threshold=0B\n                                                                Memory Pool: G1 Survivor Space (Heap memory): committed=4.000MiB, used=1.342MiB, max=-1B, threshold=?\n                                                                Memory Pool: CodeHeap 'non-profiled nmethods' (Non-heap memory): committed=2.500MiB, used=2.462MiB, max=117.2MiB, threshold=0B\n                                                                "}
{"time":"2025-06-29 12:51:01.814+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\n                                                                --------------------------------------------------------------------------------\n                                                                                        [ Operating system information ]                        \n                                                                --------------------------------------------------------------------------------\n                                                                Operating System: Linux; version: **********-microsoft-standard-WSL2; arch: amd64; cpus: 6\n                                                                Max number of file descriptors: 1048576\n                                                                Number of open file descriptors: 265\n                                                                Process id: 8\n                                                                Byte order: LITTLE_ENDIAN\n                                                                Local timezone: Etc/UTC\n                                                                Memory page size: 4096\n                                                                Unaligned memory access allowed: true\n                                                                "}
{"time":"2025-06-29 12:51:01.812+0000","level":"INFO","category":"o.n.d.d.DatabaseLifecycles","message":"Creating 'DatabaseId{00000000[system]}'."}
{"time":"2025-06-29 12:51:01.815+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\n                                                                --------------------------------------------------------------------------------\n                                                                                              [ JVM information ]                               \n                                                                --------------------------------------------------------------------------------\n                                                                VM Name: OpenJDK 64-Bit Server VM\n                                                                VM Vendor: Eclipse Adoptium\n                                                                VM Version: 21.0.7+6-LTS\n                                                                JIT compiler: HotSpot 64-Bit Tiered Compilers\n                                                                VM Arguments: [-XX:+UseG1GC, -XX:-OmitStackTraceInFastThrow, -XX:+AlwaysPreTouch, -XX:+UnlockExperimentalVMOptions, -XX:+TrustFinalNonStaticFields, -XX:+DisableExplicitGC, -Djdk.nio.maxCachedBufferSize=1024, -Dio.netty.tryReflectionSetAccessible=true, -Dio.netty.leakDetection.level=DISABLED, -Djdk.tls.ephemeralDHKeySize=2048, -Djdk.tls.rejectClientInitiatedRenegotiation=true, -XX:FlightRecorderOptions=stackdepth=256, -XX:+UnlockDiagnosticVMOptions, -XX:+DebugNonSafepoints, --add-opens=java.base/java.nio=ALL-UNNAMED, --add-opens=java.base/java.io=ALL-UNNAMED, --add-opens=java.base/sun.nio.ch=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent=ALL-UNNAMED, --enable-native-access=ALL-UNNAMED, -Dlog4j2.disable.jmx=true, -Dlog4j.layout.jsonTemplate.maxStringLength=32768, -Dfile.encoding=UTF-8]\n                                                                "}
{"time":"2025-06-29 12:51:01.815+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\n                                                                --------------------------------------------------------------------------------\n                                                                                               [ Java classpath ]                               \n                                                                --------------------------------------------------------------------------------\n                                                                 [classpath] /var/lib/neo4j/lib/asm-9.8.jar\n                                                                 [classpath] /var/lib/neo4j/lib/grpc-protobuf-lite-1.71.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-command-line-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-codec-socks-4.1.121.Final.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jaxb-runtime-2.3.2.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-ee8-webapp-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/cypher-v5-ast-factory-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/antlr4-runtime-4.13.2.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-io-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-ast-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jna-5.17.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-import-util-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/cypher-preparser-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-bolt-connection-2.0.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/magnolia_2.13-1.1.8.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-security-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/cypher-v25-antlr-parser-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/cypher-parser-factory-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/commons-compress-1.27.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/commons-codec-1.18.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jackson-jaxrs-base-2.18.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-browser-2025.5.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-codec-http2-4.1.121.Final.jar\n                                                                 [classpath] /var/lib/neo4j/conf/*\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-query-router-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/WMI4Java-1.6.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-procedure-api-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/shiro-crypto-hash-2.0.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jakarta.xml.bind-api-2.3.2.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jProcesses-1.6.5.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-resolver-4.1.121.Final.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-monitoring-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/hk2-locator-2.6.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-tcnative-classes-2.0.70.Final.jar\n                                                                 [classpath] /var/lib/neo4j/lib/cypher-parser-ast-common-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/grpc-util-1.71.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/cypher-parser-common-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-transport-native-kqueue-4.1.121.Final-osx-x86_64.jar\n                                                                 [classpath] /var/lib/neo4j/lib/snappy-java-********.jar\n                                                                 [classpath] /var/lib/neo4j/lib/eclipse-collections-api-11.1.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/shiro-config-core-2.0.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jansi-2.4.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/server-api-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/parquet-format-structures-1.15.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-ee8-security-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-security-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-common-4.1.121.Final.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jline-terminal-3.21.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/ipaddress-5.5.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/grpc-api-1.71.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/lucene-queryparser-9.11.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cypher-slotted-runtime-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-transport-4.1.121.Final.jar\n                                                                 [classpath] /var/lib/neo4j/lib/shiro-core-2.0.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-util-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-ee-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/arrow-format-18.2.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/grpc-protobuf-1.71.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/annotations-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/kiama_2.13-2.5.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jakarta.ws.rs-api-2.1.6.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-codec-4.1.121.Final.jar\n                                                                 [classpath] /var/lib/neo4j/lib/shiro-hashes-argon2-2.0.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-server-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/lucene-analysis-common-9.11.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/zstd-proxy-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cypher-config-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/parquet-encoding-1.15.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/istack-commons-runtime-3.0.8.jar\n                                                                 [classpath] /var/lib/neo4j/lib/parquet-common-1.15.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-server-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-servlet-api-4.0.6.jar\n                                                                 [classpath] /var/lib/neo4j/lib/eclipse-collections-11.1.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cloud-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-graphdb-api-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-index-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-data-collector-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cypher-macros-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-transport-classes-kqueue-4.1.121.Final.jar\n                                                                 [classpath] /var/lib/neo4j/lib/reactor-core-3.7.4.jar\n                                                                 [classpath] /var/lib/neo4j/lib/cypher-v25-ast-factory-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-values-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/shiro-event-2.0.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/picocli-4.7.6.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jakarta.validation-api-2.0.2.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-bolt-connection-pooled-2.0.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-layout-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/hk2-utils-2.6.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/stax-ex-1.8.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/txw2-2.3.2.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-codec-http-4.1.121.Final.jar\n                                                                 [classpath] /var/lib/neo4j/lib/log4j-api-2.20.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/shiro-hashes-bcrypt-2.0.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-transport-native-epoll-4.1.121.Final-linux-x86_64.jar\n                                                                 [classpath] /var/lib/neo4j/lib/argparse4j-0.9.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/log4j-core-2.20.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-capabilities-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-transport-classes-epoll-4.1.121.Final.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-id-generator-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cypher-cache-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-ee8-servlet-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/arrow-memory-core-18.2.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jackson-databind-2.18.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/lucene-backward-codecs-9.11.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-storage-engine-util-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/scala-library-2.13.16.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-unsafe-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/proto-google-common-protos-2.51.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cypher-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-bolt-connection-routed-2.0.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/grpc-stub-1.71.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-buffer-4.1.121.Final.jar\n                                                                 [classpath] /var/lib/neo4j/lib/shiro-crypto-cipher-2.0.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-consistency-check-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-front-end-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-configuration-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-http2-hpack-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/scala-collection-contrib_2.13-0.3.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jersey-container-servlet-2.43.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-graph-algo-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cypher-physical-planning-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-alpn-java-server-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/asm-tree-9.8.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-schema-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/commons-io-2.18.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jPowerShell-3.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/reactive-streams-1.0.4.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-resource-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-kernel-api-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/perfmark-api-0.27.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/cypher-shell-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/cypher-antlr-common-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/asm-util-9.8.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-fabric-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-transport-native-kqueue-4.1.121.Final-osx-aarch_64.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-wal-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/caffeine-3.2.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-alpn-server-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/commons-logging-1.3.5.jar\n                                                                 [classpath] /var/lib/neo4j/lib/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jaxb-api-2.2.12.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-bolt-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jersey-container-servlet-core-2.43.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-rewriting-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/flight-core-18.2.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jersey-client-2.43.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jackson-core-2.18.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jersey-server-2.43.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-http2-common-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-collections-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/javax.annotation-api-1.3.2.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-import-api-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-bootcheck-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/shiro-lang-2.0.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-slf4j-provider-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-util-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cypher-planner-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-expressions-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jackson-annotations-2.18.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-kernel-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-dbms-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/commons-text-1.13.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-token-api-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-io-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-procedure-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-concurrent-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/aircompressor-2.0.2.jar\n                                                                 [classpath] /var/lib/neo4j/lib/protobuf-java-util-3.25.6.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-bolt-connection-netty-2.0.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-native-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/cypher-v5-literal-interpreter-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/guava-33.4.7-jre.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jackson-datatype-jsr310-2.18.2.jar\n                                                                 [classpath] /var/lib/neo4j/lib/commons-configuration2-2.11.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-push-to-cloud-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jline-terminal-jansi-3.21.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-ssl-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/hk2-api-2.6.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/zstd-jni-1.5.7-2.jar\n                                                                 [classpath] /var/lib/neo4j/lib/arrow-vector-18.2.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jackson-jaxrs-json-provider-2.18.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-handler-proxy-4.1.121.Final.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-spatial-index-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/parquet-column-1.15.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/arrow-memory-netty-buffer-patch-18.2.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cypher-ir-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-udc-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/asm-analysis-9.8.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-record-storage-engine-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-transport-native-epoll-4.1.121.Final-linux-aarch_64.jar\n                                                                 [classpath] /var/lib/neo4j/lib/scala-reflect-2.13.16.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cypher-runtime-util-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/grpc-netty-1.71.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-csv-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jsr305-3.0.2.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-http-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cypher-rendering-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jakarta.activation-api-1.2.2.jar\n                                                                 [classpath] /var/lib/neo4j/plugins/*\n                                                                 [classpath] /var/lib/neo4j/lib/commons-lang3-3.17.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/grpc-core-1.71.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-common-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-handler-4.1.121.Final.jar\n                                                                 [classpath] /var/lib/neo4j/lib/failureaccess-1.0.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jettison-1.5.4.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jakarta.inject-2.6.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/protobuf-java-3.25.6.jar\n                                                                 [classpath] /var/lib/neo4j/lib/parquet-hadoop-1.15.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/slf4j-api-2.0.9.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cypher-interpreted-runtime-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/cypher-antlr-ast-common-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-diagnostics-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jline-reader-3.21.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/gson-2.11.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/FastInfoset-1.2.16.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cypher-dsl-2024.5.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/javassist-3.30.2-GA.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-codegen-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-ee8-nested-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jersey-hk2-2.43.jar\n                                                                 [classpath] /var/lib/neo4j/lib/cypher-v25-parser-listener-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-import-tool-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-lock-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/cypher-v5-antlr-parser-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/grpc-context-1.71.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/parquet-jackson-1.15.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/flatbuffers-java-24.3.25.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-lucene-index-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/cypher-v5-parser-listener-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/netty-transport-native-unix-common-4.1.121.Final.jar\n                                                                 [classpath] /var/lib/neo4j/lib/parquet-floor-1.50.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jakarta.annotation-api-1.3.5.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jctools-core-4.0.5.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-logging-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jackson-module-jaxb-annotations-2.18.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cypher-expression-evaluator-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cypher-logical-plans-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-gql-status-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/arrow-memory-netty-18.2.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-java-driver-5.28.5.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-http2-server-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-session-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/shiro-cache-2.0.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/shiro-crypto-core-2.0.3.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-notifications-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jersey-common-2.43.jar\n                                                                 [classpath] /var/lib/neo4j/lib/log4j-layout-template-json-2.20.0.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-cypher-planner-spi-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/neo4j-exceptions-2025.05.1.jar\n                                                                 [classpath] /var/lib/neo4j/lib/jetty-xml-12.0.17.jar\n                                                                 [classpath] /var/lib/neo4j/lib/lucene-core-9.11.1.jar\n                                                                "}
{"time":"2025-06-29 12:51:01.819+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\n                                                                --------------------------------------------------------------------------------\n                                                                                                [ Library path ]                                \n                                                                --------------------------------------------------------------------------------\n                                                                /usr/java/packages/lib\n                                                                /usr/lib64\n                                                                /lib64\n                                                                /lib\n                                                                /usr/lib\n                                                                "}
{"time":"2025-06-29 12:51:01.824+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\n                                                                --------------------------------------------------------------------------------\n                                                                                             [ System properties ]                              \n                                                                --------------------------------------------------------------------------------\n                                                                jdk.tls.rejectClientInitiatedRenegotiation = true\n                                                                sun.jnu.encoding = UTF-8\n                                                                log4j.layout.jsonTemplate.maxStringLength = 32768\n                                                                sun.arch.data.model = 64\n                                                                user.timezone = Etc/UTC\n                                                                io.netty.leakDetection.level = DISABLED\n                                                                sun.java.launcher = SUN_STANDARD\n                                                                sun.boot.library.path = /opt/java/openjdk/lib\n                                                                sun.java.command = org.neo4j.server.Neo4jCommunity --home-dir=/var/lib/neo4j --config-dir=/var/lib/neo4j/conf --console-mode\n                                                                jdk.debug = release\n                                                                io.netty.tryReflectionSetAccessible = true\n                                                                sun.cpu.endian = little\n                                                                user.home = /var/lib/neo4j\n                                                                user.language = en\n                                                                file.separator = /\n                                                                jdk.tls.ephemeralDHKeySize = 2048\n                                                                sun.management.compiler = HotSpot 64-Bit Tiered Compilers\n                                                                user.name = neo4j\n                                                                stdout.encoding = UTF-8\n                                                                jdk.nio.maxCachedBufferSize = 1024\n                                                                path.separator = :\n                                                                file.encoding = UTF-8\n                                                                jnidispatch.path = /var/lib/neo4j/.cache/JNA/temp/jna2466660158581694286.tmp\n                                                                jna.platform.library.path = /usr/lib/x86_64-linux-gnu:/lib/x86_64-linux-gnu:/lib64:/usr/lib:/lib\n                                                                jna.loaded = true\n                                                                user.dir = /var/lib/neo4j\n                                                                native.encoding = UTF-8\n                                                                stderr.encoding = UTF-8\n                                                                sun.io.unicode.encoding = UnicodeLittle\n                                                                log4j2.disable.jmx = true\n                                                                "}
{"time":"2025-06-29 12:51:01.828+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\n                                                                --------------------------------------------------------------------------------\n                                                                                      [ (IANA) TimeZone database version ]                      \n                                                                --------------------------------------------------------------------------------\n                                                                  TimeZone version: 2025a (available for 603 zone identifiers)\n                                                                "}
{"time":"2025-06-29 12:51:01.834+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\n                                                                --------------------------------------------------------------------------------\n                                                                                            [ Network information ]                             \n                                                                --------------------------------------------------------------------------------\n                                                                Interface eth0:\n                                                                    address: **********\n                                                                Interface lo:\n                                                                    address: 0:0:0:0:0:0:0:1%lo\n                                                                    address: 127.0.0.1\n                                                                "}
{"time":"2025-06-29 12:51:01.835+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\n                                                                --------------------------------------------------------------------------------\n                                                                                         [ Native access information ]                          \n                                                                --------------------------------------------------------------------------------\n                                                                Native access details: Linux native access is available.\n                                                                "}
{"time":"2025-06-29 12:51:01.835+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\n                                                                --------------------------------------------------------------------------------\n                                                                                                [ DBMS config ]                                 \n                                                                --------------------------------------------------------------------------------\n                                                                DBMS provided settings:\n                                                                internal.dbms.bolt.local_address=882a8ab1-5057-4d52-a606-c75c8af48ced\n                                                                server.bolt.enabled=true\n                                                                server.default_listen_address=0.0.0.0\n                                                                server.directories.configuration=/var/lib/neo4j/conf\n                                                                server.directories.import=/var/lib/neo4j/import\n                                                                server.directories.logs=/logs\n                                                                server.directories.neo4j_home=/var/lib/neo4j\n                                                                server.http.enabled=true\n                                                                server.https.enabled=false\n                                                                server.jvm.additional=-XX:+UseG1GC\n-XX:-OmitStackTraceInFastThrow\n-XX:+AlwaysPreTouch\n-XX:+UnlockExperimentalVMOptions\n-XX:+TrustFinalNonStaticFields\n-XX:+DisableExplicitGC\n-Djdk.nio.maxCachedBufferSize=1024\n-Dio.netty.tryReflectionSetAccessible=true\n-Dio.netty.leakDetection.level=DISABLED\n-Djdk.tls.ephemeralDHKeySize=2048\n-Djdk.tls.rejectClientInitiatedRenegotiation=true\n-XX:FlightRecorderOptions=stackdepth=256\n-XX:+UnlockDiagnosticVMOptions\n-XX:+DebugNonSafepoints\n--add-opens=java.base/java.nio=ALL-UNNAMED\n--add-opens=java.base/java.io=ALL-UNNAMED\n--add-opens=java.base/sun.nio.ch=ALL-UNNAMED\n--add-opens=java.base/java.util.concurrent=ALL-UNNAMED\n--enable-native-access=ALL-UNNAMED\n-Dlog4j2.disable.jmx=true\n-Dlog4j.layout.jsonTemplate.maxStringLength=32768\n                                                                server.memory.pagecache.size=512.00MiB\n                                                                server.windows_service_name=neo4j\n                                                                Directories in use:\n                                                                server.directories.configuration=/var/lib/neo4j/conf\n                                                                server.directories.data=/var/lib/neo4j/data\n                                                                server.directories.dumps.root=/var/lib/neo4j/data/dumps\n                                                                server.directories.import=/var/lib/neo4j/import\n                                                                server.directories.lib=/var/lib/neo4j/lib\n                                                                server.directories.licenses=/var/lib/neo4j/licenses\n                                                                server.directories.logs=/logs\n                                                                server.directories.neo4j_home=/var/lib/neo4j\n                                                                server.directories.plugins=/var/lib/neo4j/plugins\n                                                                server.directories.run=/var/lib/neo4j/run\n                                                                server.directories.script.root=/var/lib/neo4j/data/scripts\n                                                                server.directories.transaction.logs.root=/var/lib/neo4j/data/transactions\n                                                                "}
{"time":"2025-06-29 12:51:01.836+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\n                                                                --------------------------------------------------------------------------------\n                                                                                                 [ Packaging ]                                  \n                                                                --------------------------------------------------------------------------------\n                                                                Edition: Community\n                                                                Package Type: docker bullseye\n                                                                "}
{"time":"2025-06-29 12:51:01.837+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"\n                                                                --------------------------------------------------------------------------------\n                                                                                           [ Global Server Identity ]                           \n                                                                --------------------------------------------------------------------------------\n                                                                Registered ServerId{3ff82403}\n                                                                "}
{"time":"2025-06-29 12:51:02.089+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Using connector transport epoll"}
{"time":"2025-06-29 12:51:02.146+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Configured external Bolt connector with listener address /0.0.0.0:7687"}
{"time":"2025-06-29 12:51:02.147+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Bolt server loaded"}
{"time":"2025-06-29 12:51:02.176+0000","level":"INFO","category":"o.n.d.d.DatabaseLifecycles","message":"Starting 'DatabaseId{00000000[system]}'."}
{"time":"2025-06-29 12:51:03.288+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"[system/00000000] \n                                                                [system/00000000] ********************************************************************************\n                                                                [system/00000000]                               [ Database: system ]                              \n                                                                [system/00000000] ********************************************************************************\n                                                                [system/00000000] --------------------------------------------------------------------------------\n                                                                [system/00000000]                                   [ Version ]                                   \n                                                                [system/00000000] --------------------------------------------------------------------------------\n                                                                [system/00000000] DBMS: community record-aligned-1.1\n                                                                [system/00000000] Kernel version: 2025.05.1\n                                                                [system/00000000] \n                                                                [system/00000000] --------------------------------------------------------------------------------\n                                                                [system/00000000]                                 [ Store files ]                                 \n                                                                [system/00000000] --------------------------------------------------------------------------------\n                                                                [system/00000000] Disk space on partition (Total / Free / Free %): 499171454976 / 107494301696 / 21\n                                                                [system/00000000] Storage files stored on file store: 9p\n                                                                [system/00000000] Storage files: (filename : modification date - size)\n                                                                [system/00000000]   neostore: 2025-06-29 12:51:02.842+0000 - 8.000KiB\n                                                                [system/00000000]   neostore.counts.db: 2025-06-29 12:51:02.864+0000 - 0B\n                                                                [system/00000000]   neostore.indexstats.db: 2025-06-29 12:51:02.890+0000 - 0B\n                                                                [system/00000000]   neostore.labeltokenstore.db: 2025-06-29 12:51:02.813+0000 - 0B\n                                                                [system/00000000]   neostore.labeltokenstore.db.id: 2025-06-29 12:51:02.808+0000 - 0B\n                                                                [system/00000000]   neostore.labeltokenstore.db.names: 2025-06-29 12:51:02.804+0000 - 8.000KiB\n                                                                [system/00000000]   neostore.labeltokenstore.db.names.id: 2025-06-29 12:51:02.800+0000 - 0B\n                                                                [system/00000000]   neostore.nodestore.db: 2025-06-29 12:51:02.728+0000 - 0B\n                                                                [system/00000000]   neostore.nodestore.db.id: 2025-06-29 12:51:02.724+0000 - 0B\n                                                                [system/00000000]   neostore.nodestore.db.labels: 2025-06-29 12:51:02.698+0000 - 8.000KiB\n                                                                [system/00000000]   neostore.nodestore.db.labels.id: 2025-06-29 12:51:02.672+0000 - 0B\n                                                                [system/00000000]   neostore.propertystore.db: 2025-06-29 12:51:02.777+0000 - 0B\n                                                                [system/00000000]   neostore.propertystore.db.arrays: 2025-06-29 12:51:02.768+0000 - 8.000KiB\n                                                                [system/00000000]   neostore.propertystore.db.arrays.id: 2025-06-29 12:51:02.763+0000 - 0B\n                                                                [system/00000000]   neostore.propertystore.db.id: 2025-06-29 12:51:02.773+0000 - 0B\n                                                                [system/00000000]   neostore.propertystore.db.index: 2025-06-29 12:51:02.751+0000 - 0B\n                                                                [system/00000000]   neostore.propertystore.db.index.id: 2025-06-29 12:51:02.748+0000 - 0B\n                                                                [system/00000000]   neostore.propertystore.db.index.keys: 2025-06-29 12:51:02.737+0000 - 8.000KiB\n                                                                [system/00000000]   neostore.propertystore.db.index.keys.id: 2025-06-29 12:51:02.732+0000 - 0B\n                                                                [system/00000000]   neostore.propertystore.db.strings: 2025-06-29 12:51:02.760+0000 - 8.000KiB\n                                                                [system/00000000]   neostore.propertystore.db.strings.id: 2025-06-29 12:51:02.754+0000 - 0B\n                                                                [system/00000000]   neostore.relationshipgroupstore.db: 2025-06-29 12:51:02.832+0000 - 8.000KiB\n                                                                [system/00000000]   neostore.relationshipgroupstore.db.id: 2025-06-29 12:51:02.827+0000 - 0B\n                                                                [system/00000000]   neostore.relationshipgroupstore.degrees.db: 2025-06-29 12:51:02.879+0000 - 0B\n                                                                [system/00000000]   neostore.relationshipstore.db: 2025-06-29 12:51:02.783+0000 - 0B\n                                                                [system/00000000]   neostore.relationshipstore.db.id: 2025-06-29 12:51:02.780+0000 - 0B\n                                                                [system/00000000]   neostore.relationshiptypestore.db: 2025-06-29 12:51:02.798+0000 - 0B\n                                                                [system/00000000]   neostore.relationshiptypestore.db.id: 2025-06-29 12:51:02.795+0000 - 0B\n                                                                [system/00000000]   neostore.relationshiptypestore.db.names: 2025-06-29 12:51:02.790+0000 - 8.000KiB\n                                                                [system/00000000]   neostore.relationshiptypestore.db.names.id: 2025-06-29 12:51:02.786+0000 - 0B\n                                                                [system/00000000]   neostore.schemastore.db: 2025-06-29 12:51:02.823+0000 - 8.000KiB\n                                                                [system/00000000]   neostore.schemastore.db.id: 2025-06-29 12:51:02.818+0000 - 0B\n                                                                [system/00000000] Storage summary: \n                                                                [system/00000000]   Total size of store: 72.00KiB\n                                                                [system/00000000]   Total size of mapped files: 72.00KiB\n                                                                [system/00000000] \n                                                                [system/00000000] --------------------------------------------------------------------------------\n                                                                [system/00000000]                               [ Transaction log ]                               \n                                                                [system/00000000] --------------------------------------------------------------------------------\n                                                                [system/00000000] Transaction log files stored on file store: 9p\n                                                                [system/00000000] Transaction log metadata:\n                                                                [system/00000000]  - current kernel version used in transactions: V2025_05\n                                                                [system/00000000]  - last committed transaction id: 1\n                                                                [system/00000000] Transaction log files:\n                                                                [system/00000000]  - existing transaction log versions: -1--1\n                                                                [system/00000000]  - no transactions found\n                                                                [system/00000000] Checkpoint log files:\n                                                                [system/00000000]  - existing checkpoint log versions: -1--1\n                                                                [system/00000000]  - no checkpoints found\n                                                                [system/00000000] \n                                                                [system/00000000] --------------------------------------------------------------------------------\n                                                                [system/00000000]                                   [ Id usage ]                                  \n                                                                [system/00000000] --------------------------------------------------------------------------------\n                                                                [system/00000000] ArrayPropertyStore[neostore.nodestore.db.labels]: used=1 high=0\n                                                                [system/00000000] NodeStore[neostore.nodestore.db]: used=0 high=-1\n                                                                [system/00000000] StringPropertyStore[neostore.propertystore.db.index.keys]: used=1 high=0\n                                                                [system/00000000] PropertyIndexStore[neostore.propertystore.db.index]: used=0 high=-1\n                                                                [system/00000000] StringPropertyStore[neostore.propertystore.db.strings]: used=1 high=0\n                                                                [system/00000000] ArrayPropertyStore[neostore.propertystore.db.arrays]: used=1 high=0\n                                                                [system/00000000] PropertyStore[neostore.propertystore.db]: used=0 high=-1\n                                                                [system/00000000] RelationshipStore[neostore.relationshipstore.db]: used=0 high=-1\n                                                                [system/00000000] StringPropertyStore[neostore.relationshiptypestore.db.names]: used=1 high=0\n                                                                [system/00000000] RelationshipTypeStore[neostore.relationshiptypestore.db]: used=0 high=-1\n                                                                [system/00000000] StringPropertyStore[neostore.labeltokenstore.db.names]: used=1 high=0\n                                                                [system/00000000] LabelTokenStore[neostore.labeltokenstore.db]: used=0 high=-1\n                                                                [system/00000000] SchemaStore[neostore.schemastore.db]: used=1 high=0\n                                                                [system/00000000] RelationshipGroupStore[neostore.relationshipgroupstore.db]: used=1 high=0\n                                                                [system/00000000] NeoStore[neostore]: used=-1 high=-1\n                                                                [system/00000000] \n                                                                [system/00000000] --------------------------------------------------------------------------------\n                                                                [system/00000000]                                   [ Metadata ]                                  \n                                                                [system/00000000] --------------------------------------------------------------------------------\n                                                                [system/00000000] EXTERNAL_STORE_UUID (Database identifier exposed as external store identity. Generated on creation and never updated): 85206e2f-c958-47f5-a394-76a657567edb\n                                                                [system/00000000] DATABASE_ID (The last used DatabaseId for this database): null\n                                                                [system/00000000] LEGACY_STORE_VERSION (Legacy store format version. This field is used from 5.0 onwards only to distinguish non-migrated pre 5.0 metadata stores.): -3523014627327384477\n                                                                [system/00000000] STORE_ID (Store ID): StoreId{creationTime=1751201463075, random=-9116204070696103053, storageEngineName='record', formatName='aligned', majorVersion=1, minorVersion=1}\n                                                                [system/00000000] "}
{"time":"2025-06-29 12:51:03.327+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[system/00000000] Requirement `Database unavailable` makes database system unavailable.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:03.329+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[system/00000000] DatabaseId{00000000[system]} is unavailable.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:03.354+0000","level":"WARN","category":"o.n.k.i.s.MetaDataStore","message":"[system/00000000] Missing counts store, rebuilding it.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:03.365+0000","level":"WARN","category":"o.n.k.i.s.MetaDataStore","message":"[system/00000000] Counts store rebuild completed.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:16.097+0000","level":"INFO","category":"o.n.k.d.Database","message":"[system/00000000] Starting transaction log [/data/transactions/system/neostore.transaction.db.0] at version=0","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:16.883+0000","level":"INFO","category":"o.n.k.d.Database","message":"[system/00000000] Starting transaction log [/data/transactions/system/checkpoint.0] at version=0","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:16.914+0000","level":"INFO","category":"o.n.k.i.t.l.f.c.CheckpointLogFile","message":"[system/00000000] Scanning log file with version 0 for checkpoint entries","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:16.942+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[system/00000000] Fulfilling of requirement 'Database unavailable' makes database system available.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:16.946+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[system/00000000] DatabaseId{00000000[system]} is ready.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:17.216+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:17.353+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:17.386+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:17.406+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:17.427+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[system/00000000] Checkpoint triggered by \"Database init completed.\" @ txId: 3, append index: 3 checkpoint started...","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:17.647+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[system/00000000] Checkpoint triggered by \"Database init completed.\" @ txId: 3, append index: 3 checkpoint completed in 219ms. Checkpoint flushed 74 pages (0% of total available pages), in 57 IOs. Checkpoint performed with IO limit: unlimited, paused in total 0 times( 0 millis).","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:17.651+0000","level":"INFO","category":"o.n.k.i.t.l.p.LogPruningImpl","message":"[system/00000000] No log version pruned. The strategy used was '2 days 2147483648 size'. ","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:17.661+0000","level":"INFO","category":"o.n.u.UserDataCollector","message":"Anonymous Usage Data is being sent to Neo4j, see https://neo4j.com/docs/usage-data/"}
{"time":"2025-06-29 12:51:17.792+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=3, name='constraint_798238d6', type='RANGE', schema=(:DatabaseName {name, namespace}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:17.833+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=3, name='constraint_798238d6', type='RANGE', schema=(:DatabaseName {name, namespace}), indexProvider='range-1.0' )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:17.868+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] Constraint Index( id=3, name='constraint_798238d6', type='RANGE', schema=(:Label[1] {PropertyKey[11], PropertyKey[12]}), indexProvider='range-1.0' ) is ONLINE.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:17.891+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=5, name='constraint_8014b60a', type='RANGE', schema=(:Database {name}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:17.911+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=5, name='constraint_8014b60a', type='RANGE', schema=(:Database {name}), indexProvider='range-1.0' )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:17.930+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] Constraint Index( id=5, name='constraint_8014b60a', type='RANGE', schema=(:Label[2] {PropertyKey[11]}), indexProvider='range-1.0' ) is ONLINE.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:18.244+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=7, name='constraint_5789ae3', type='RANGE', schema=(:User {name}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:18.267+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=7, name='constraint_5789ae3', type='RANGE', schema=(:User {name}), indexProvider='range-1.0' )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:18.288+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] Constraint Index( id=7, name='constraint_5789ae3', type='RANGE', schema=(:Label[3] {PropertyKey[11]}), indexProvider='range-1.0' ) is ONLINE.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:18.304+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=9, name='constraint_74fad970', type='RANGE', schema=(:User {id}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:18.325+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=9, name='constraint_74fad970', type='RANGE', schema=(:User {id}), indexProvider='range-1.0' )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:18.347+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] Constraint Index( id=9, name='constraint_74fad970', type='RANGE', schema=(:Label[3] {PropertyKey[28]}), indexProvider='range-1.0' ) is ONLINE.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:18.359+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index population started: [Index( id=11, name='auth-constraint', type='RANGE', schema=(:Auth {provider, id}), indexProvider='range-1.0' )]","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:18.381+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[system/00000000] Index creation finished for index [Index( id=11, name='auth-constraint', type='RANGE', schema=(:Auth {provider, id}), indexProvider='range-1.0' )].","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:18.400+0000","level":"INFO","category":"o.n.k.i.a.i.IndexingService","message":"[system/00000000] Constraint Index( id=11, name='auth-constraint', type='RANGE', schema=(:Label[4] {PropertyKey[29], PropertyKey[28]}), indexProvider='range-1.0' ) is ONLINE.","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 12:51:18.403+0000","level":"INFO","category":"o.n.s.s.s.UserSecurityGraphComponent","message":"Initializing system graph model for component 'security-users' with version -1 and status UNINITIALIZED"}
{"time":"2025-06-29 12:51:18.418+0000","level":"INFO","category":"o.n.s.s.s.UserSecurityGraphComponent","message":"Setting up initial user from `auth.ini` file: neo4j"}
{"time":"2025-06-29 12:51:18.419+0000","level":"INFO","category":"o.n.s.s.a.CommunitySecurityModule","message":"CREATE USER neo4j PASSWORD ****** CHANGE NOT REQUIRED","message":"CREATE USER neo4j PASSWORD ****** CHANGE NOT REQUIRED","source":"","type":"security"}
{"time":"2025-06-29 12:51:18.441+0000","level":"INFO","category":"o.n.s.s.s.UserSecurityGraphComponent","message":"Setting version for 'security-users' to 5"}
{"time":"2025-06-29 12:51:18.447+0000","level":"INFO","category":"o.n.s.s.s.UserSecurityGraphComponent","message":"After initialization of system graph model component 'security-users' have version 5 and status CURRENT"}
{"time":"2025-06-29 12:51:18.454+0000","level":"INFO","category":"o.n.s.s.s.UserSecurityGraphComponent","message":"Performing postInitialization step for component 'security-users' with version 5 and status CURRENT"}
{"time":"2025-06-29 12:51:18.464+0000","level":"INFO","category":"o.n.d.d.DatabaseLifecycles","message":"Creating 'DatabaseId{85bd5aad[neo4j]}'."}
{"time":"2025-06-29 12:51:18.470+0000","level":"INFO","category":"o.n.d.d.DatabaseLifecycles","message":"Starting 'DatabaseId{85bd5aad[neo4j]}'."}
{"time":"2025-06-29 12:51:18.499+0000","level":"INFO","category":"o.n.k.i.s.f.RecordFormatSelector","message":"[neo4j/85bd5aad] Selected configured format for store /data/databases/neo4j: RecordFormat:PageAlignedV5_0[aligned-1.1]","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:18.667+0000","level":"INFO","category":"o.n.i.d.DiagnosticsManager","message":"[neo4j/85bd5aad] \n                                                                [neo4j/85bd5aad] ********************************************************************************\n                                                                [neo4j/85bd5aad]                               [ Database: neo4j ]                               \n                                                                [neo4j/85bd5aad] ********************************************************************************\n                                                                [neo4j/85bd5aad] --------------------------------------------------------------------------------\n                                                                [neo4j/85bd5aad]                                   [ Version ]                                   \n                                                                [neo4j/85bd5aad] --------------------------------------------------------------------------------\n                                                                [neo4j/85bd5aad] DBMS: community record-aligned-1.1\n                                                                [neo4j/85bd5aad] Kernel version: 2025.05.1\n                                                                [neo4j/85bd5aad] \n                                                                [neo4j/85bd5aad] --------------------------------------------------------------------------------\n                                                                [neo4j/85bd5aad]                                 [ Store files ]                                 \n                                                                [neo4j/85bd5aad] --------------------------------------------------------------------------------\n                                                                [neo4j/85bd5aad] Disk space on partition (Total / Free / Free %): 499171454976 / 107222921216 / 21\n                                                                [neo4j/85bd5aad] Storage files stored on file store: 9p\n                                                                [neo4j/85bd5aad] Storage files: (filename : modification date - size)\n                                                                [neo4j/85bd5aad]   neostore: 2025-06-29 12:51:18.695+0000 - 8.000KiB\n                                                                [neo4j/85bd5aad]   neostore.counts.db: 2025-06-29 12:51:18.697+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.indexstats.db: 2025-06-29 12:51:18.703+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.labeltokenstore.db: 2025-06-29 12:51:18.678+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.labeltokenstore.db.id: 2025-06-29 12:51:18.674+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.labeltokenstore.db.names: 2025-06-29 12:51:18.671+0000 - 8.000KiB\n                                                                [neo4j/85bd5aad]   neostore.labeltokenstore.db.names.id: 2025-06-29 12:51:18.668+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.nodestore.db: 2025-06-29 12:51:18.618+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.nodestore.db.id: 2025-06-29 12:51:18.615+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.nodestore.db.labels: 2025-06-29 12:51:18.612+0000 - 8.000KiB\n                                                                [neo4j/85bd5aad]   neostore.nodestore.db.labels.id: 2025-06-29 12:51:18.606+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.propertystore.db: 2025-06-29 12:51:18.647+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.propertystore.db.arrays: 2025-06-29 12:51:18.641+0000 - 8.000KiB\n                                                                [neo4j/85bd5aad]   neostore.propertystore.db.arrays.id: 2025-06-29 12:51:18.638+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.propertystore.db.id: 2025-06-29 12:51:18.645+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.propertystore.db.index: 2025-06-29 12:51:18.629+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.propertystore.db.index.id: 2025-06-29 12:51:18.627+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.propertystore.db.index.keys: 2025-06-29 12:51:18.623+0000 - 8.000KiB\n                                                                [neo4j/85bd5aad]   neostore.propertystore.db.index.keys.id: 2025-06-29 12:51:18.619+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.propertystore.db.strings: 2025-06-29 12:51:18.636+0000 - 8.000KiB\n                                                                [neo4j/85bd5aad]   neostore.propertystore.db.strings.id: 2025-06-29 12:51:18.632+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.relationshipgroupstore.db: 2025-06-29 12:51:18.690+0000 - 8.000KiB\n                                                                [neo4j/85bd5aad]   neostore.relationshipgroupstore.db.id: 2025-06-29 12:51:18.686+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.relationshipgroupstore.degrees.db: 2025-06-29 12:51:18.699+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.relationshipstore.db: 2025-06-29 12:51:18.652+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.relationshipstore.db.id: 2025-06-29 12:51:18.650+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.relationshiptypestore.db: 2025-06-29 12:51:18.665+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.relationshiptypestore.db.id: 2025-06-29 12:51:18.663+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.relationshiptypestore.db.names: 2025-06-29 12:51:18.659+0000 - 8.000KiB\n                                                                [neo4j/85bd5aad]   neostore.relationshiptypestore.db.names.id: 2025-06-29 12:51:18.655+0000 - 0B\n                                                                [neo4j/85bd5aad]   neostore.schemastore.db: 2025-06-29 12:51:18.684+0000 - 8.000KiB\n                                                                [neo4j/85bd5aad]   neostore.schemastore.db.id: 2025-06-29 12:51:18.680+0000 - 0B\n                                                                [neo4j/85bd5aad] Storage summary: \n                                                                [neo4j/85bd5aad]   Total size of store: 72.00KiB\n                                                                [neo4j/85bd5aad]   Total size of mapped files: 72.00KiB\n                                                                [neo4j/85bd5aad] \n                                                                [neo4j/85bd5aad] --------------------------------------------------------------------------------\n                                                                [neo4j/85bd5aad]                               [ Transaction log ]                               \n                                                                [neo4j/85bd5aad] --------------------------------------------------------------------------------\n                                                                [neo4j/85bd5aad] Transaction log files stored on file store: 9p\n                                                                [neo4j/85bd5aad] Transaction log metadata:\n                                                                [neo4j/85bd5aad]  - current kernel version used in transactions: V2025_05\n                                                                [neo4j/85bd5aad]  - last committed transaction id: 1\n                                                                [neo4j/85bd5aad] Transaction log files:\n                                                                [neo4j/85bd5aad]  - existing transaction log versions: -1--1\n                                                                [neo4j/85bd5aad]  - no transactions found\n                                                                [neo4j/85bd5aad] Checkpoint log files:\n                                                                [neo4j/85bd5aad]  - existing checkpoint log versions: -1--1\n                                                                [neo4j/85bd5aad]  - no checkpoints found\n                                                                [neo4j/85bd5aad] \n                                                                [neo4j/85bd5aad] --------------------------------------------------------------------------------\n                                                                [neo4j/85bd5aad]                                   [ Id usage ]                                  \n                                                                [neo4j/85bd5aad] --------------------------------------------------------------------------------\n                                                                [neo4j/85bd5aad] ArrayPropertyStore[neostore.nodestore.db.labels]: used=1 high=0\n                                                                [neo4j/85bd5aad] NodeStore[neostore.nodestore.db]: used=0 high=-1\n                                                                [neo4j/85bd5aad] StringPropertyStore[neostore.propertystore.db.index.keys]: used=1 high=0\n                                                                [neo4j/85bd5aad] PropertyIndexStore[neostore.propertystore.db.index]: used=0 high=-1\n                                                                [neo4j/85bd5aad] StringPropertyStore[neostore.propertystore.db.strings]: used=1 high=0\n                                                                [neo4j/85bd5aad] ArrayPropertyStore[neostore.propertystore.db.arrays]: used=1 high=0\n                                                                [neo4j/85bd5aad] PropertyStore[neostore.propertystore.db]: used=0 high=-1\n                                                                [neo4j/85bd5aad] RelationshipStore[neostore.relationshipstore.db]: used=0 high=-1\n                                                                [neo4j/85bd5aad] StringPropertyStore[neostore.relationshiptypestore.db.names]: used=1 high=0\n                                                                [neo4j/85bd5aad] RelationshipTypeStore[neostore.relationshiptypestore.db]: used=0 high=-1\n                                                                [neo4j/85bd5aad] StringPropertyStore[neostore.labeltokenstore.db.names]: used=1 high=0\n                                                                [neo4j/85bd5aad] LabelTokenStore[neostore.labeltokenstore.db]: used=0 high=-1\n                                                                [neo4j/85bd5aad] SchemaStore[neostore.schemastore.db]: used=1 high=0\n                                                                [neo4j/85bd5aad] RelationshipGroupStore[neostore.relationshipgroupstore.db]: used=1 high=0\n                                                                [neo4j/85bd5aad] NeoStore[neostore]: used=-1 high=-1\n                                                                [neo4j/85bd5aad] \n                                                                [neo4j/85bd5aad] --------------------------------------------------------------------------------\n                                                                [neo4j/85bd5aad]                                   [ Metadata ]                                  \n                                                                [neo4j/85bd5aad] --------------------------------------------------------------------------------\n                                                                [neo4j/85bd5aad] EXTERNAL_STORE_UUID (Database identifier exposed as external store identity. Generated on creation and never updated): da037c09-9045-4681-8bed-8a78ef9fa92c\n                                                                [neo4j/85bd5aad] DATABASE_ID (The last used DatabaseId for this database): null\n                                                                [neo4j/85bd5aad] LEGACY_STORE_VERSION (Legacy store format version. This field is used from 5.0 onwards only to distinguish non-migrated pre 5.0 metadata stores.): -3523014627327384477\n                                                                [neo4j/85bd5aad] STORE_ID (Store ID): StoreId{creationTime=1751201478589, random=-6345019051058255331, storageEngineName='record', formatName='aligned', majorVersion=1, minorVersion=1}\n                                                                [neo4j/85bd5aad] "}
{"time":"2025-06-29 12:51:18.682+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[neo4j/85bd5aad] Requirement `Database unavailable` makes database neo4j unavailable.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:18.683+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[neo4j/85bd5aad] DatabaseId{85bd5aad[neo4j]} is unavailable.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:18.691+0000","level":"WARN","category":"o.n.k.i.s.MetaDataStore","message":"[neo4j/85bd5aad] Missing counts store, rebuilding it.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:18.694+0000","level":"WARN","category":"o.n.k.i.s.MetaDataStore","message":"[neo4j/85bd5aad] Counts store rebuild completed.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:31.669+0000","level":"INFO","category":"o.n.k.d.Database","message":"[neo4j/85bd5aad] Starting transaction log [/data/transactions/neo4j/neostore.transaction.db.0] at version=0","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:31.735+0000","level":"INFO","category":"o.n.k.d.Database","message":"[neo4j/85bd5aad] Starting transaction log [/data/transactions/neo4j/checkpoint.0] at version=0","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:31.766+0000","level":"INFO","category":"o.n.k.i.t.l.f.c.CheckpointLogFile","message":"[neo4j/85bd5aad] Scanning log file with version 0 for checkpoint entries","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:31.787+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[neo4j/85bd5aad] Fulfilling of requirement 'Database unavailable' makes database neo4j available.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:31.787+0000","level":"INFO","category":"o.n.k.a.DatabaseAvailabilityGuard","message":"[neo4j/85bd5aad] DatabaseId{85bd5aad[neo4j]} is ready.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:31.797+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[neo4j/85bd5aad] Index population started: [Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' )]","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:31.831+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[neo4j/85bd5aad] Index creation finished for index [Index( id=2, name='index_f7700477', type='LOOKUP', schema=()-[:<any-types>]-(), indexProvider='token-lookup-1.0' )].","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:31.854+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[neo4j/85bd5aad] Index population started: [Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' )]","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:31.878+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[neo4j/85bd5aad] Index creation finished for index [Index( id=1, name='index_343aff4e', type='LOOKUP', schema=(:<any-labels>), indexProvider='token-lookup-1.0' )].","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:31.893+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[neo4j/85bd5aad] Checkpoint triggered by \"Database init completed.\" @ txId: 3, append index: 3 checkpoint started...","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:32.016+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[neo4j/85bd5aad] Checkpoint triggered by \"Database init completed.\" @ txId: 3, append index: 3 checkpoint completed in 122ms. Checkpoint flushed 74 pages (0% of total available pages), in 57 IOs. Checkpoint performed with IO limit: unlimited, paused in total 0 times( 0 millis).","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:32.017+0000","level":"INFO","category":"o.n.k.i.t.l.p.LogPruningImpl","message":"[neo4j/85bd5aad] No log version pruned. The strategy used was '2 days 2147483648 size'. ","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:51:32.060+0000","level":"INFO","category":"o.n.b.p.c.c.n.SocketNettyConnector","message":"Bolt enabled on 0.0.0.0:7687."}
{"time":"2025-06-29 12:51:32.062+0000","level":"WARN","category":"i.n.b.ServerBootstrap","message":"Unknown channel option 'SO_REUSEADDR' for channel '[id: 0x2bfcfe74]'"}
{"time":"2025-06-29 12:51:32.069+0000","level":"INFO","category":"o.n.b.BoltServer","message":"Bolt server started"}
{"time":"2025-06-29 12:51:32.069+0000","level":"INFO","category":"o.n.s.A.ServerComponentsLifecycleAdapter","message":"Starting web server"}
{"time":"2025-06-29 12:51:33.167+0000","level":"INFO","category":"o.n.s.CommunityNeoWebServer","message":"HTTP enabled on 0.0.0.0:7474."}
{"time":"2025-06-29 12:51:33.176+0000","level":"INFO","category":"o.n.s.CommunityNeoWebServer","message":"Remote interface available at http://localhost:7474/"}
{"time":"2025-06-29 12:51:33.176+0000","level":"INFO","category":"o.n.s.A.ServerComponentsLifecycleAdapter","message":"Web server started."}
{"time":"2025-06-29 12:51:33.180+0000","level":"INFO","category":"o.n.g.f.DatabaseManagementServiceFactory","message":"id: 6BB6C0F350B273E5FCFDDA4F9E3CAB8727B2181015F544F2AEB7EA46326A24D5"}
{"time":"2025-06-29 12:51:33.181+0000","level":"INFO","category":"o.n.g.f.DatabaseManagementServiceFactory","message":"name: system"}
{"time":"2025-06-29 12:51:33.181+0000","level":"INFO","category":"o.n.g.f.DatabaseManagementServiceFactory","message":"creationDate: 2025-06-29T12:51:03.075Z"}
{"time":"2025-06-29 12:56:39.754+0000","level":"INFO","category":"o.n.s.q.d.LocalChannelDriverFactory","message":"Direct driver instance 780604127 created for server address http-driver.com:0"}
{"time":"2025-06-29 12:56:57.207+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[neo4j/85bd5aad] Index population started: [Index( id=3, name='search', type='FULLTEXT', schema=(:Memory {name, type, observations}), indexProvider='fulltext-1.0' )]","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 12:56:57.860+0000","level":"INFO","category":"o.n.k.i.a.i.IndexPopulationJob","message":"[neo4j/85bd5aad] Index creation finished for index [Index( id=3, name='search', type='FULLTEXT', schema=(:Memory {name, type, observations}), indexProvider='fulltext-1.0' )].","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:06:49.101+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[system/00000000] Checkpoint triggered by \"Scheduled checkpoint for every 15 minutes threshold\" @ txId: 45, append index: 45 checkpoint started...","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 13:06:49.496+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[system/00000000] Checkpoint triggered by \"Scheduled checkpoint for every 15 minutes threshold\" @ txId: 45, append index: 45 checkpoint completed in 394ms. Checkpoint flushed 73 pages (0% of total available pages), in 73 IOs. Checkpoint performed with IO limit: unlimited, paused in total 0 times( 0 millis).","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 13:06:49.497+0000","level":"INFO","category":"o.n.k.i.t.l.p.LogPruningImpl","message":"[system/00000000] No log version pruned. The strategy used was '2 days 2147483648 size'. ","databaseId":"00000000-0000-0000-0000-000000000001","databaseName":"system"}
{"time":"2025-06-29 13:07:03.300+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[neo4j/85bd5aad] Checkpoint triggered by \"Scheduled checkpoint for every 15 minutes threshold\" @ txId: 24, append index: 24 checkpoint started...","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:07:03.648+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[neo4j/85bd5aad] Checkpoint triggered by \"Scheduled checkpoint for every 15 minutes threshold\" @ txId: 24, append index: 24 checkpoint completed in 347ms. Checkpoint flushed 53 pages (0% of total available pages), in 53 IOs. Checkpoint performed with IO limit: unlimited, paused in total 0 times( 0 millis).","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:07:03.649+0000","level":"INFO","category":"o.n.k.i.t.l.p.LogPruningImpl","message":"[neo4j/85bd5aad] No log version pruned. The strategy used was '2 days 2147483648 size'. ","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:22:28.235+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[neo4j/85bd5aad] Checkpoint triggered by \"Scheduled checkpoint for every 15 minutes threshold\" @ txId: 30, append index: 30 checkpoint started...","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:22:28.592+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[neo4j/85bd5aad] Checkpoint triggered by \"Scheduled checkpoint for every 15 minutes threshold\" @ txId: 30, append index: 30 checkpoint completed in 356ms. Checkpoint flushed 55 pages (0% of total available pages), in 53 IOs. Checkpoint performed with IO limit: unlimited, paused in total 0 times( 0 millis).","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:22:28.593+0000","level":"INFO","category":"o.n.k.i.t.l.p.LogPruningImpl","message":"[neo4j/85bd5aad] No log version pruned. The strategy used was '2 days 2147483648 size'. ","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:33:02.971+0000","level":"INFO","category":"o.n.c.i.c.CypherQueryCaches","message":"[neo4j/85bd5aad] Discarded stale query from the query cache after 1660 seconds. Reason: NodesAllCardinality changed from 10.0 to 25.0, which is a divergence of 0.6 which is greater than threshold 0.****************. Query id: 252.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:33:02.971+0000","level":"INFO","category":"o.n.c.i.c.CypherQueryCaches","message":"[neo4j/85bd5aad] Discarded stale query from the query cache after 1660 seconds. Reason: NodesAllCardinality changed from 10.0 to 25.0, which is a divergence of 0.6 which is greater than threshold 0.****************. Query id: 253.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:33:02.973+0000","level":"INFO","category":"o.n.c.i.c.CypherQueryCaches","message":"[neo4j/85bd5aad] Discarded stale query from the query cache after 1660 seconds. Reason: NodesAllCardinality changed from 10.0 to 25.0, which is a divergence of 0.6 which is greater than threshold 0.****************. Query id: 251.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:35:25.856+0000","level":"INFO","category":"o.n.c.i.c.CypherQueryCaches","message":"[neo4j/85bd5aad] Discarded stale query from the query cache after 246 seconds. Reason: NodesWithLabelCardinality(Some(LabelId(0))) changed from 10.0 to 40.0, which is a divergence of 0.75 which is greater than threshold 0.706844452372892. Query id: 279.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:35:25.900+0000","level":"INFO","category":"o.n.c.i.c.CypherQueryCaches","message":"[neo4j/85bd5aad] Discarded stale query from the query cache after 190 seconds. Reason: NodesWithLabelCardinality(Some(LabelId(0))) changed from 10.0 to 40.0, which is a divergence of 0.75 which is greater than threshold 0.7165678023717948. Query id: 281.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:35:25.936+0000","level":"INFO","category":"o.n.c.i.c.CypherQueryCaches","message":"[neo4j/85bd5aad] Discarded stale query from the query cache after 190 seconds. Reason: NodesWithLabelCardinality(Some(LabelId(0))) changed from 10.0 to 40.0, which is a divergence of 0.75 which is greater than threshold 0.7165814054587896. Query id: 282.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:35:25.969+0000","level":"INFO","category":"o.n.c.i.c.CypherQueryCaches","message":"[neo4j/85bd5aad] Discarded stale query from the query cache after 190 seconds. Reason: NodesWithLabelCardinality(Some(LabelId(0))) changed from 10.0 to 40.0, which is a divergence of 0.75 which is greater than threshold 0.7165960691050088. Query id: 283.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:35:26.005+0000","level":"INFO","category":"o.n.c.i.c.CypherQueryCaches","message":"[neo4j/85bd5aad] Discarded stale query from the query cache after 190 seconds. Reason: NodesWithLabelCardinality(Some(LabelId(0))) changed from 10.0 to 40.0, which is a divergence of 0.75 which is greater than threshold 0.7166125001685562. Query id: 284.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:37:53.920+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[neo4j/85bd5aad] Checkpoint triggered by \"Scheduled checkpoint for every 15 minutes threshold\" @ txId: 105, append index: 105 checkpoint started...","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:37:54.306+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[neo4j/85bd5aad] Checkpoint triggered by \"Scheduled checkpoint for every 15 minutes threshold\" @ txId: 105, append index: 105 checkpoint completed in 384ms. Checkpoint flushed 59 pages (0% of total available pages), in 51 IOs. Checkpoint performed with IO limit: unlimited, paused in total 0 times( 0 millis).","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:37:54.306+0000","level":"INFO","category":"o.n.k.i.t.l.p.LogPruningImpl","message":"[neo4j/85bd5aad] No log version pruned. The strategy used was '2 days 2147483648 size'. ","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:44:37.789+0000","level":"INFO","category":"o.n.c.i.c.CypherQueryCaches","message":"[neo4j/85bd5aad] Discarded stale query from the query cache after 551 seconds. Reason: NodesWithLabelCardinality(Some(LabelId(0))) changed from 40.0 to 123.0, which is a divergence of 0.6747967479674797 which is greater than threshold 0.****************. Query id: 433.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:45:42.432+0000","level":"INFO","category":"o.n.c.i.c.CypherQueryCaches","message":"[neo4j/85bd5aad] Discarded stale query from the query cache after 2535 seconds. Reason: NodesAllCardinality changed from 10.0 to 142.0, which is a divergence of 0.9295774647887324 which is greater than threshold 0.*****************. Query id: 456.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:48:29.596+0000","level":"INFO","category":"o.n.c.i.c.CypherQueryCaches","message":"[neo4j/85bd5aad] Discarded stale query from the query cache after 2367 seconds. Reason: NodesAllCardinality changed from 10.0 to 142.0, which is a divergence of 0.9295774647887324 which is greater than threshold 0.****************. Query id: 703.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:48:38.916+0000","level":"INFO","category":"o.n.c.i.c.CypherQueryCaches","message":"[neo4j/85bd5aad] Discarded stale query from the query cache after 176 seconds. Reason: CardinalityByLabelsAndRelationshipType(None,None,None) changed from 3.0 to 36.0, which is a divergence of 0.9166666666666666 which is greater than threshold 0.7191214830563103. Query id: 706.","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:53:20.478+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[neo4j/85bd5aad] Checkpoint triggered by \"Scheduled checkpoint for every 15 minutes threshold\" @ txId: 322, append index: 322 checkpoint started...","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:53:20.876+0000","level":"INFO","category":"o.n.k.i.t.l.c.CheckPointerImpl","message":"[neo4j/85bd5aad] Checkpoint triggered by \"Scheduled checkpoint for every 15 minutes threshold\" @ txId: 322, append index: 322 checkpoint completed in 397ms. Checkpoint flushed 81 pages (0% of total available pages), in 63 IOs. Checkpoint performed with IO limit: unlimited, paused in total 0 times( 0 millis).","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
{"time":"2025-06-29 13:53:20.876+0000","level":"INFO","category":"o.n.k.i.t.l.p.LogPruningImpl","message":"[neo4j/85bd5aad] No log version pruned. The strategy used was '2 days 2147483648 size'. ","databaseId":"85bd5aad-1ae8-4473-8bcb-ad48cc0f0446","databaseName":"neo4j"}
