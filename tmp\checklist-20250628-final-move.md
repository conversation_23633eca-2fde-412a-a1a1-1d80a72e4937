# Checklist: Final Document Relocation

**Status:** FAILED

- [ ] **1. Read Source:** Read the content of `C:\Users\<USER>\.gemini\cda-matrix-ref\mistral-system-prompt.md`. **<-- FAILED: File not found.**
- [ ] **2. Write Destination:** Write the content to `C:\Users\<USER>\.gemini\docs\system-prompts\mistral-system-prompt.md`.
- [ ] **3. Delete Original:** Propose a plan to delete the original source file to complete the "move".
- [ ] **4. Verify:** Confirm the final state is correct.

---
**Log:**
- **2025-06-28:** Task initiated.
- **2025-06-28:** Step 1 failed. The source file does not exist, indicating a previous operational error where the file was not copied as assumed. Halting task.