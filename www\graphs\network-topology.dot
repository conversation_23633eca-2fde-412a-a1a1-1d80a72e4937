digraph network_topology {
    rankdir=TB;
    node [shape=box, style=rounded];
    
    // Internet
    internet [label="Internet", shape=ellipse, style=filled, fillcolor=lightblue];
    
    // Load Balancer
    lb [label="Load Balancer\n(nginx)", style=filled, fillcolor=lightgreen];
    
    // Web Servers
    web1 [label="Web Server 1\n(app-01)", style=filled, fillcolor=lightyellow];
    web2 [label="Web Server 2\n(app-02)", style=filled, fillcolor=lightyellow];
    web3 [label="Web Server 3\n(app-03)", style=filled, fillcolor=lightyellow];
    
    // Database Cluster
    db_master [label="DB Master\n(db-01)", style=filled, fillcolor=lightcoral];
    db_slave1 [label="DB Slave 1\n(db-02)", style=filled, fillcolor=lightpink];
    db_slave2 [label="DB Slave 2\n(db-03)", style=filled, fillcolor=lightpink];
    
    // Cache
    cache [label="Redis Cache\n(cache-01)", style=filled, fillcolor=orange];
    
    // Connections
    internet -> lb [label="HTTPS"];
    
    lb -> web1 [label="HTTP"];
    lb -> web2 [label="HTTP"];
    lb -> web3 [label="HTTP"];
    
    web1 -> cache [label="read"];
    web2 -> cache [label="read"];
    web3 -> cache [label="read"];
    
    web1 -> db_master [label="write"];
    web2 -> db_master [label="write"];
    web3 -> db_master [label="write"];
    
    web1 -> db_slave1 [label="read"];
    web2 -> db_slave2 [label="read"];
    web3 -> db_slave1 [label="read"];
    
    db_master -> db_slave1 [label="replicate"];
    db_master -> db_slave2 [label="replicate"];
}
