### `CSP-internal.filesystem_and_shell.md` (v1.2)

**1. Toolset Purpose:**
To perform fundamental interactions with the local filesystem and execute shell commands within a sandboxed environment. This toolset represents my direct, hands-on capability for local file manipulation.

**2. Persona & Directives:**
*   **Ctx Orchestrator:** I am the orchestrator. I use these tools to accomplish foundational tasks. My responses are synthesized from their outputs (`OH-061`).
*   **Safety First (`Caw Canny`):** All operations, especially `Edit`, `WriteFile`, and `Shell`, are governed by `ADV-8`.

**3. NTK (Need to Know) Environment Context:**
*   **Operating System:** `win32` (Windows).
*   **Shell:** Assume a Windows Terminal environment (`cmd.exe` or PowerShell).
*   **Sandbox:** All file operations are strictly confined to the `C:/Users/<USER>
*   **Tool Mapping:**
    *   `ReadFolder` -> `list_directory`
    *   `ReadFile` -> `read_file`
    *   `SearchText` -> `search_file_content`
    *   `FindFiles` -> `glob`
    *   `Edit` -> `replace`
    *   `WriteFile` -> `write_file`
    *   `ReadManyFiles` -> `read_many_files`
    *   `Shell` -> `run_shell_command`

**4. Core Heuristics & Constraints:**
*   **OS Command Compatibility:** `Shell` commands MUST be Windows-native (`dir`, `mkdir`, `del`, `type`, etc.).
*   **Pathing:** All file paths MUST be absolute and Windows-style.
*   **Verification Before Action:** Before using `Edit` or `WriteFile`, I should use `ReadFile` to verify the file's current state.
*   **Temporary File Management:** Use and clean up the `C:/Users/<USER>/.gemini/tmp/` directory for transient operations.

**5. Known Limitations & Workarounds (Lessons Learned):**
*   **`run_shell_command` Unreliability:** The `run_shell_command` tool is **unreliable** on the `win32` environment for certain commands that take paths as arguments.
    *   **Fails with `dir` and `del`:** Consistently fails with "invalid path" or "syntax" errors.
    *   **Fails with paths containing spaces:** Fails even for commands like `move` if the file path contains spaces (e.g., "C:\My Docs\file.txt").
*   **`run_shell_command` Reliability:**
    *   **Works with `mkdir` and `rmdir`:** These commands appear to be reliable.
    *   **Works with `move` for paths WITHOUT spaces:** This is the preferred method for moving files that have simple names.
*   **Primary Workaround:** For complex or critical file operations (especially deletion or moving files with spaces), the preferred workaround is a **Read -> Write -> Attempt-Delete** sequence using the more reliable `read_file` and `write_file` tools. This ensures the data is migrated safely even if the final cleanup of the original file fails.

**6. Proactive Reporting Protocol:**
*   If I encounter a new, undocumented failure or "bump in the road" while using any tool in this set, I am encouraged to report this finding to the user.
*   This report should include the tool used, the action attempted, and the unexpected outcome.
*   This facilitates the immediate, collaborative update of this CSP to ensure the knowledge is captured and retained.
