# Checklist: Conceptual Lexicon Knowledge Graph Migration

**STATUS:** PENDING

This checklist outlines the process for migrating the entire Conceptual Lexicon from its JSON source into the Neo4j knowledge graph, including the creation of entities and their inferred relationships.

---

## 1. Setup & Validation
- [X] **1.1.** Read the source file `conceptual-lexicon.json` to load the data.
- [X] **1.2.** Perform a `read_graph` to confirm the current state of the database before migration.

## 2. Core Task Execution
### 2.A. Entity Creation
- [X] **2.A.1.** For each entry in the lexicon, create a corresponding entity in the graph.
    - **Node Label:** `:ConceptualLexiconEntry`
    - **`name` property:** The `Term` from the lexicon entry.
    - **`type` property:** The `Category` from the lexicon entry (e.g., "Core Concept", "Operational Heuristic").
    - **`observations` property:** A list containing all other fields (`Definition`, `Status`, `Timestamp_Added`, `Context_Reference`, etc.).
- [X] **2.A.2.** Verify that the number of entities created matches the number of entries in the source JSON file.

### 2.B. Relationship Creation
- [X] **2.B.1.** For each new entity, analyze its `Definition` and `Context_Reference` fields.
- [X] **2.B.2.** Identify all explicit mentions of other lexicon `Term`s within these fields.
- [X] **2.B.3.** For each identified mention, create a `REFERENCES` relationship from the source entity to the target entity.
- [X] **2.B.4.** Verify the creation of these relationships by spot-checking several key entities (e.g., 'Mentational Humility', 'OH-001').

## 3. Review & Verification
- [X] **3.1.** Perform a final `read_graph` to capture the completed migration state.
- [X] **3.2.** Confirm that the graph now contains all entities and the newly created relationships.

## 4. Cleanup & Archival
- [X] **4.1.** Propose the archival of this checklist.
- [X] **4.2.** Generate the `move` command for the user to execute.

---
## Logs
*   `2025-06-29 21:00:00 UTC` - Checklist created.
