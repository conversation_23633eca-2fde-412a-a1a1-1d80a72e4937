# Environment Variables

This document outlines the key environment variables that configure the Ctx CLI application's behavior.

| Variable               | Description                                                                                              | Default/Example Value                  |
| :--------------------- | :------------------------------------------------------------------------------------------------------- | :------------------------------------- |
| `GEMINI_SYSTEM_MD`     | Specifies the absolute path to the core system prompt file, which contains the Ctx persona's CDA and CL. | `C:/Users/<USER>/.gemini/system.md`     |
| `GEMINI_SETTINGS_JSON` | Points to the JSON file containing session settings, such as user ID and memory shard paths.             | `C:/Users/<USER>/.gemini/settings.json` |
| `GEMINI_USER_ID_FILE`  | The path to a simple text file containing the current user's ID.                                         | `C:/Users/<USER>/.gemini/user_id`       |

---

_This document is a work in progress and will be updated as new configuration options are introduced._

## Additions by pjsvis

- GEMINI_API_KEY: The API key for the Gemini CLI.
- GEMINI_SYSTEM_MD: The path to the system.md file or if set to true will use the default system.md file. We should check the docs for this
- GITHUB_PERSONAL_ACCESS_TOKEN: The personal access token for GitHub.
- GOOGLE_CLOUD_PROJECT_ID: The project ID for Google Cloud, required for non-personal users.
- REMOVE the following entries
- There is no ENV entry for GEMINI_SETTINGS_JSON: The path to the settings.json file.
- There is no ENV entry for GEMINI_USER_ID_FILE: The path to the user_id file.
- There is no ENV entry for GEMINI_MEMORY_SHARD_PATH: The path to the memory shard directory.
- There is no ENV entry for GEMINI_ENABLE_STARTUP_CONTEXT_RESTORE: Whether to enable startup context restore.
- There is no ENV entry for GEMINI_MEMORY_FILE_PATH: The path to the memory file.
