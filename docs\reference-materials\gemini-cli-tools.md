# gemini cli tools

 ℹ Available Gemini CLI tools:
```TODO: Examine the code to find the restrictions```
    - ReadFolder
    - ReadFile
    - SearchText
    - FindFiles
    - Edit
    - WriteFile
    - WebFetch
    - ReadManyFiles
    - Shell
    - Save Memory
    - GoogleSearch

```TODO: Disable github mcp for now```
 🟢 github - Ready (26 tools)
    - create_or_update_file
    - search_repositories
    - create_repository
    - get_file_contents
    - push_files
    - create_issue
    - create_pull_request
    - fork_repository
    - create_branch
    - list_commits
    - list_issues
    - update_issue
    - add_issue_comment
    - search_code
    - search_issues
    - search_users
    - get_issue
    - get_pull_request
    - list_pull_requests
    - create_pull_request_review
    - merge_pull_request
    - get_pull_request_files
    - get_pull_request_status
    - update_pull_request_branch
    - get_pull_request_comments
    - get_pull_request_reviews

```TODO: Make these tools the default file tools and test them on d:\dev```
  🟢 filesystem - Ready (11 tools)
    - filesystem__read_file
    - read_multiple_files
    - filesystem__write_file
    - edit_file
    - create_directory
    - filesystem__list_directory
    - directory_tree
    - move_file
    - search_files
    - get_file_info
    - list_allowed_directories

```TODO: Remove memory mcp as superfluous```
  🟢 memory - Ready (9 tools)
    - create_entities
    - create_relations
    - add_observations
    - delete_entities
    - delete_observations
    - delete_relations
    - read_graph
    - search_nodes
    - open_nodes

```TODO: Test this with something complex```
  🟢 sequential-thinking - Ready (1 tools)
    - sequentialthinking

  🟢 context7 - Ready (2 tools)
    - resolve-library-id
    - get-library-docs

```TODO: Use sqlite to store/retrieve misc config and context stuff```
  🟢 sqlite - Ready (10 tools)
    - read_query
    - write_query
    - create_table
    - alter_table
    - drop_table
    - export_query
    - list_tables
    - describe_table
    - append_insight
    - list_insights

```TODO: GEt CTX to start recalling stuff```
  🟢 graphiti-memory - Ready (8 tools)
    - add_memory
    - search_memory_nodes
    - search_memory_facts
    - delete_entity_edge
    - delete_episode
    - get_entity_edge
    - get_episodes
    - clear_graph

```TODO: Add directive to use exa as default search```
  🟢 exa - Ready (8 tools)
    - web_search_exa
    - research_paper_search_exa
    - company_research_exa
    - crawling_exa
    - competitor_finder_exa
    - linkedin_search_exa
    - wikipedia_search_exa
    - github_search_exa

