# Internal & MCP Tools Testing Checklist

**Version:** 1.0
**Last Updated:** 2025-06-28

This document provides a streamlined checklist for testing the functionality of internal and MCP toolsets. For detailed explanations of each step, refer to `internal_tools_reference.md`.

---

### `internal.filesystem_and_shell`

- [ ] **Pre-flight:** Review `CSP-internal.filesystem_and_shell.md`.
- [ ] **Setup:** Create a test directory (e.g., `.../tmp/fs_test`).
- [ ] **`write_file`:** Write content to a test file inside the test directory.
- [ ] **`read_file`:** Read the test file and verify its content.
- [ ] **`replace`:**
    - [ ] Read the file to get context.
    - [ ] Replace a portion of the content.
    - [ ] Read the file again to verify the replacement.
- [ ] **`list_directory`:** List the contents of the test directory to verify file presence.
- [ ] **Cleanup:**
    - [ ] Use `write_file` with empty content to clear the test file.
    - [ ] Use `run_shell_command` with `rmdir` to remove the (now empty) test directory.

---

### `internal.web_and_memory`

- [ ] **Pre-flight:** Review `CSP-internal.web_and_memory.md`.
- [ ] **`google_web_search`:** Execute a simple search query.
- [ ] **`web_fetch`:** Fetch and summarize a known, stable URL.
- [ ] **`save_memory`:** Save a simple, factual string.

---

### `mcp.sqlite`

- [ ] **Pre-flight:** Review `CSP-mcp.sqlite.md`.
- [ ] **`list_tables`:** Check initial state.
- [ ] **`create_table`:** Create a uniquely named test table.
- [ ] **`describe_table`:** Verify the new table's schema.
- [ ] **`write_query`:** `INSERT` a test row.
- [ ] **`read_query`:** `SELECT` the test row to verify the write.
- [ ] **`drop_table`:** Drop the test table with `confirm=True`.

---

### `mcp.graphiti-memory` (Non-Destructive)

- [ ] **Pre-flight:** Review `CSP-mcp.graphiti-memory.md`.
- [ ] **`add_memory`:** Add a test episode with a unique, date-stamped name.
- [ ] **`get_episodes`:** Retrieve the latest episode to confirm addition and capture its `uuid`. *Note: May require a short delay and retry due to async processing.*
- [ ] **`search_memory_facts`:** Search for facts related to the test episode.
- [ ] **`delete_episode`:** Delete **only** the test episode using its captured `uuid`.

---

### `mcp.exa`

- [ ] **Pre-flight:** Review `CSP-mcp.exa.md`.
- [ ] **`web_search_exa`:** Attempt a general search query.
- [ ] **Note:** If the first test fails with a timeout, assume the service is down and halt further testing of this toolset.

---

### `mcp.context7`

- [ ] **Pre-flight:** Review `CSP-mcp.context7.md`.
- [ ] **`resolve-library-id`:** Resolve a common library name (e.g., 'react').
- [ ] **`get-library-docs`:** Use the ID from the previous step to fetch documentation for a specific topic.

---

### `mcp.sequential-thinking`

- [ ] **Pre-flight:** Review `CSP-mcp.sequential-thinking.md`.
- [ ] **`sequentialthinking`:** Execute a simple, two-step thought process to verify the tool's stateful logic.
