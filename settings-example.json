{"selectedAuthType": "gemini-api-key", "theme": "<PERSON><PERSON>", "mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"]}, "github": {"url": "https://api.githubcopilot.com/mcp/", "authorization_token": "Bearer ${GITHUB_PERSONAL_ACCESS_TOKEN}"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "sqlite": {"command": "npx", "args": ["-y", "@executeautomation/database-server", "c:/Users/<USER>/.gemini/data/database.db"]}, "graphiti-memory": {"transport": "sse", "url": "http://localhost:8000/sse"}, "exa": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "exa", "--key", "${EXA_API_KEY}"]}}}