// Final version of the ingestion script for capability checklists
// This version assumes a more normalized schema
LOAD CSV WITH HEADERS FROM 'file:///capability_checklist_v3.csv' AS row
// Merge Checklist, Category, and Tool
MERGE (cl:Checklist {id: row.checklist_id})
MERGE (cat:Category {name: row.category})
MERGE (tool:Tool {name: row.tool_name})
// Create a unique Test instance for each row
CREATE (test:Test {
  test_id: row.test_id,
  status: row.status,
  result: row.result,
  comment: row.comment,
  timestamp: datetime()
})
// Create relationships
MERGE (cl)-[:CONTAINS_CATEGORY]->(cat)
MERGE (cat)-[:GROUPS_TOOL]->(tool)
MERGE (tool)-[:HAS_TEST]->(test)
RETURN count(*) AS tests_ingested
