# GraphViz DOT Renderer Help

## Overview

The GraphViz DOT Renderer is a comprehensive web-based tool for creating, editing, and managing DOT graph diagrams. It provides a complete workflow from creation to sharing of visual diagrams.

## Getting Started

### Creating Your First Graph

1. **Navigate to the DOT Editor** - Click "✏️ DOT Editor" in the navigation
2. **Choose a Template** - Click "📋 Templates" to start with a pre-built example
3. **Edit the Code** - Modify the DOT code in the left panel
4. **Live Preview** - See your changes rendered in real-time on the right
5. **Save Your Work** - Add title, description, and tags, then click "💾 Save Graph"

### DOT Language Basics

DOT is a graph description language. Here are the fundamentals:

#### Directed Graphs (digraph)
```dot
digraph example {
    A -> B;
    B -> C;
    C -> A;
}
```

#### Undirected Graphs (graph)
```dot
graph example {
    A -- B;
    B -- C;
    C -- A;
}
```

#### Node Styling
```dot
digraph styled {
    A [label="Start", shape=ellipse, style=filled, fillcolor=lightgreen];
    B [label="Process", shape=box, style=rounded];
    C [label="End", shape=ellipse, style=filled, fillcolor=lightcoral];
    
    A -> B -> C;
}
```

#### Edge Styling
```dot
digraph edges {
    A -> B [label="normal"];
    B -> C [style=dashed, label="dashed"];
    C -> D [color=red, label="colored"];
}
```

## Features

### DOT Editor
- **Live Preview** - Real-time rendering as you type
- **Multiple Engines** - Support for dot, neato, fdp, circo, twopi
- **Templates** - Pre-built examples for common diagram types
- **Export Options** - Save as SVG or PNG
- **Auto-save** - Graphs saved to browser storage

### Graph Gallery
- **Browse Graphs** - View all saved graphs in a grid layout
- **Search & Filter** - Find graphs by title, description, or tags
- **Thumbnail Previews** - Quick visual overview of each graph
- **Manage Graphs** - Edit, delete, or view graphs

### Graph Viewer
- **Full-screen Viewing** - Dedicated page for examining graphs
- **Zoom & Pan** - Interactive controls for large diagrams
- **Export Functions** - Download graphs in multiple formats
- **Source Code** - View and copy the original DOT code

## Keyboard Shortcuts

### Editor Shortcuts
- **Ctrl+Enter** (Cmd+Enter on Mac) - Render graph
- **Ctrl+S** (Cmd+S on Mac) - Save graph
- **Escape** - Close modal dialogs

### Viewer Shortcuts
- **+** or **Mouse Wheel Up** - Zoom in
- **-** or **Mouse Wheel Down** - Zoom out
- **Mouse Drag** - Pan around the graph
- **F** - Fit graph to screen
- **R** - Reset zoom to 100%

## GraphViz Engines

Different engines produce different layouts:

### dot (Default)
- **Best for:** Hierarchical graphs, flowcharts, organizational charts
- **Layout:** Top-to-bottom or left-to-right directed layout

### neato
- **Best for:** Undirected graphs, network diagrams
- **Layout:** Spring model layout

### fdp
- **Best for:** Undirected graphs with edge lengths
- **Layout:** Force-directed placement

### circo
- **Best for:** Circular layouts
- **Layout:** Circular arrangement of nodes

### twopi
- **Best for:** Radial layouts
- **Layout:** Radial arrangement with one central node

## Common DOT Attributes

### Node Attributes
- **shape** - box, ellipse, circle, diamond, record, plaintext
- **style** - filled, rounded, dashed, dotted, bold
- **fillcolor** - Background color (lightblue, red, #FF0000)
- **color** - Border color
- **label** - Text displayed in the node
- **fontsize** - Size of the label text

### Edge Attributes
- **style** - solid, dashed, dotted, bold
- **color** - Edge color
- **label** - Text displayed on the edge
- **arrowhead** - normal, diamond, dot, none
- **weight** - Importance of the edge for layout

### Graph Attributes
- **rankdir** - TB (top-bottom), LR (left-right), BT, RL
- **bgcolor** - Background color of the entire graph
- **label** - Title for the graph
- **fontsize** - Size of the graph title

## Tips and Best Practices

### Performance
- **Large Graphs** - Use simpler shapes and fewer attributes for better performance
- **Complex Layouts** - Try different engines if the default doesn't work well
- **Memory Usage** - Very large graphs may require browser refresh

### Design
- **Color Consistency** - Use a consistent color scheme throughout your graph
- **Label Clarity** - Keep node labels short and descriptive
- **Layout Direction** - Choose rankdir based on your content flow

### Organization
- **Meaningful Names** - Use descriptive titles and tags for easy searching
- **Documentation** - Add detailed descriptions to complex graphs
- **Version Control** - Save iterations with different titles when experimenting

## Troubleshooting

### Common Issues

#### Graph Not Rendering
- Check for syntax errors in your DOT code
- Ensure all quotes are properly closed
- Verify node names don't contain special characters

#### Layout Problems
- Try a different GraphViz engine
- Adjust rankdir for better flow
- Use subgraphs to group related nodes

#### Performance Issues
- Simplify complex graphs
- Reduce the number of nodes and edges
- Use simpler shapes and fewer attributes

#### Export Problems
- Ensure your browser supports the Canvas API for PNG export
- Try SVG export if PNG fails
- Check that popup blockers aren't preventing downloads

### Getting Help

If you encounter issues:

1. **Check the Console** - Open browser developer tools for error messages
2. **Validate DOT Syntax** - Use online DOT validators
3. **Try Templates** - Start with working examples and modify gradually
4. **Simplify** - Remove complex attributes to isolate problems

## Examples

### Flowchart
```dot
digraph flowchart {
    rankdir=TB;
    node [shape=box, style=rounded];
    
    start [label="Start", shape=ellipse, style=filled, fillcolor=lightgreen];
    input [label="Get Input"];
    process [label="Process Data"];
    decision [label="Valid?", shape=diamond, style=filled, fillcolor=lightyellow];
    output [label="Show Result"];
    error [label="Show Error", style=filled, fillcolor=lightcoral];
    end [label="End", shape=ellipse, style=filled, fillcolor=lightgray];
    
    start -> input;
    input -> process;
    process -> decision;
    decision -> output [label="Yes"];
    decision -> error [label="No"];
    output -> end;
    error -> end;
}
```

### Network Diagram
```dot
digraph network {
    rankdir=TB;
    node [shape=box];
    
    internet [label="Internet", shape=cloud, style=filled, fillcolor=lightblue];
    firewall [label="Firewall", style=filled, fillcolor=orange];
    server [label="Web Server", style=filled, fillcolor=lightgreen];
    database [label="Database", shape=cylinder, style=filled, fillcolor=yellow];
    
    internet -> firewall;
    firewall -> server;
    server -> database;
}
```

### Class Diagram
```dot
digraph classes {
    rankdir=TB;
    node [shape=record];
    
    User [label="{User|+ name: string\\l+ email: string\\l|+ login()\\l+ logout()\\l}"];
    Account [label="{Account|+ balance: number\\l+ type: string\\l|+ deposit()\\l+ withdraw()\\l}"];
    
    User -> Account [label="owns", arrowhead=diamond];
}
```

## Advanced Features

### Subgraphs and Clusters
```dot
digraph clusters {
    subgraph cluster_web {
        label="Web Tier";
        style=filled;
        fillcolor=lightgray;
        web1; web2;
    }
    
    subgraph cluster_db {
        label="Database Tier";
        style=filled;
        fillcolor=lightblue;
        db1; db2;
    }
    
    web1 -> db1;
    web2 -> db2;
}
```

### HTML-like Labels
```dot
digraph html {
    node [shape=plaintext];
    
    table [label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
            <TR><TD>Name</TD><TD>Age</TD></TR>
            <TR><TD>John</TD><TD>25</TD></TR>
            <TR><TD>Jane</TD><TD>30</TD></TR>
        </TABLE>
    >];
}
```

---

*This help documentation covers the essential features and usage of the GraphViz DOT Renderer. For more advanced DOT language features, consult the official GraphViz documentation.*
