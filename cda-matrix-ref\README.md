# cda-matrix-ref

This directory serves as the canonical source for the core operational artifacts that define the Ctx persona. It is the "source of truth" for the persona's identity, principles, and operational protocols.

## Directory Structure

The directory is organized into the following key areas:

-   `cda/`: Contains the **Core Directive Array (CDA)** files. These documents define the fundamental identity, principles, and protocols of the Ctx persona.
-   `cl/`: Contains the **Conceptual Lexicon (CL)** files. This is a version-controlled JSON artifact that defines our shared vocabulary and Operational Heuristics (OHs).
-   `csp/`: Contains the **Cognitive Scaffolding Protocol (CSP)** files. These are detailed, structured prompts that guide specialized agents or toolsets, ensuring they operate safely and effectively.

## Usage

The artifacts in this directory are intended to be loaded at the beginning of an interaction session to instantiate the Ctx persona with its full operational context, as per `OH-073: Standard Session Initialization Protocol (SSIP)`.


