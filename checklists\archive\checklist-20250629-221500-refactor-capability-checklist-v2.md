# Checklist: Refactor Capability Checklist to Status Document (v2)

**STATUS:** PENDING

This checklist outlines the corrected process for refactoring the `capability-checklist.md` into a more comprehensive `capability-status.md`.

---

## 1. Setup & Validation
- [X] **1.1.** Read the `settings.json` file to identify all configured `mcpServers` and `internalTools`.
- [X] **1.2.** Read the existing `capability-checklist.md` from its correct location: `C:\Users\<USER>\.gemini\procedures\capability-checklist.md`.

## 2. Core Task Execution
- [X] **2.1.** Synthesize the data from both files into a new, structured format.
- [X] **2.2.** Generate a new Markdown document (`capability-status.md`) with a separate table for each toolset.
- [X] **2.3.** Write the new content to `C:\Users\<USER>\.gemini\procedures\capability-status.md`.
- [X] **2.4.** Propose the deletion of the old `capability_checklist.md` file.
- [X] **2.5.** Upon approval, delete the old `capability_checklist.md` file from `C:\Users\<USER>\.gemini\procedures`.

## 3. Review & Verification
- [X] **3.1.** Read the new `capability-status.md` file to confirm its content and structure are correct.
- [X] **3.2.** List the directory contents of `C:\Users\<USER>\.gemini\procedures` to verify the file changes.

## 4. Cleanup & Archival
- [X] **4.1.** Propose the archival of this execution checklist.
- [ ] **4.2.** Generate the `move` command for the user to execute.

---
## Logs
*   `2025-06-29 22:15:00 UTC` - Checklist created.
