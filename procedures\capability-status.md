# Ctx Capability Status

This document tracks the operational status and testing of Ctx's tools, sourced from `settings.json` and manual verification.

*   **Type:** `MCP` for Model-Context-Protocol servers, `Internal` for built-in tools.
*   **Status:** `Active` if the toolset is configured in `settings.json`, `Inactive` otherwise.
*   **Tested:** Has a specific test been run for this tool? (Yes/No)
*   **Passed:** Did the test succeed and produce the expected outcome? (Yes/No/Partial)
*   **Comments:** Relevant notes on the test, outcome, or limitations.

---

## Active Toolsets

### Filesystem (MCP)
| Tool Name | Tested | Passed | Comments |
| :--- | :--- | :--- | :--- |
| `list_directory` | Yes | Yes | Functional within `C:\Users\<USER>\Users\Owner` sandbox. |
| `write_file` | Yes | Yes | Functional for overwriting. |
| `search_file_content`| Yes | Yes | Functional within the home directory. |
| `glob` | Yes | Yes | Functional within the home directory. |
| `replace` | Yes | Yes | Functional for single and multi-line replacements. |
| `read_many_files` | Yes | Yes | Functional within the home directory. |
| `run_shell_command` | Yes | Yes | Functional for simple, non-destructive commands. |

### Neo4j Memory (MCP)
| Tool Name | Tested | Passed | Comments |
| :--- | :--- | :--- | :--- |
| `create_entities` | Yes | Yes | `type` parameter cannot contain spaces or special characters. |
| `create_relations` | Yes | Yes | Requires exact `source`, `target`, and `relationType`. |
| `add_observations` | Yes | Yes | Works as expected. |
| `delete_entities` | Yes | Yes | Deletes single, unconnected entities. Cascade behavior is unknown. |
| `delete_observations` | Yes | Yes | Requires exact string match. |
| `delete_relations` | Yes | Yes | Requires exact `source`, `target`, and `relationType`. |
| `read_graph` | Yes | Yes | Works as expected. |
| `search_nodes` | Yes | Yes | Case-insensitive search of `name` and `observations`. |
| `find_nodes` | Yes | Yes | Case-sensitive exact match on `name`. |
| `open_nodes` | Yes | Yes | Appears functionally identical to `find_nodes`. |

### Sequential Thinking (MCP)
| Tool Name | Tested | Passed | Comments |
| :--- | :--- | :--- | :--- |
| `sequentialthinking` | Yes | Yes | Functional. |

### Context7 (MCP)
| Tool Name | Tested | Passed | Comments |
| :--- | :--- | :--- | :--- |
| `resolve-library-id`| Yes | Yes | Functional. |
| `get-library-docs` | Yes | Yes | Functional. |

### SQLite (MCP)
| Tool Name | Tested | Passed | Comments |
| :--- | :--- | :--- | :--- |
| `read_query` | Yes | Yes | Functional. |
| `write_query` | Yes | Yes | Functional. |
| `create_table` | Yes | Yes | Functional. |
| `alter_table` | Yes | Yes | Functional. |
| `drop_table` | Yes | Yes | Functional. |
| `export_query` | Yes | Yes | Functional. |
| `list_tables` | Yes | Yes | Functional. |
| `describe_table` | Yes | Yes | Functional. |

### Exa (MCP)
| Tool Name | Tested | Passed | Comments |
| :--- | :--- | :--- | :--- |
| `web_search_exa` | Yes | Yes | Functional. |
| `research_paper_search_exa`| Yes | Yes | Functional. |
| `company_research_exa`| Yes | Yes | Functional. |
| `crawling_exa` | Yes | Yes | Functional. |
| `competitor_finder_exa`| Yes | Yes | Functional. |
| `linkedin_search_exa`| Yes | Yes | Functional. |
| `wikipedia_search_exa`| Yes | Yes | Functional. |
| `github_search_exa` | Yes | Yes | Functional. |

### Time (MCP)
| Tool Name | Tested | Passed | Comments |
| :--- | :--- | :--- | :--- |
| `get_time` | No | | |

---

## Inactive/Deprecated Toolsets

### Internal Memory
| Tool Name | Tested | Passed | Comments |
| :--- | :--- | :--- | :--- |
| `save_memory` | Yes | Yes | Successfully saved the filesystem sandbox fact. |

### Graphiti
| Tool Name | Tested | Passed | Comments |
| :--- | :--- | :--- | :--- |
| `add_memory` | Yes | Yes | Functional after DB schema initialization. |
| `search_memory_nodes`| Yes | Yes | Functional after DB schema initialization. |
| `search_memory_facts`| Yes | Yes | Functional after DB schema initialization. |
| `delete_entity_edge`| Yes | Yes | Functional. |
| `delete_episode` | Yes | Yes | Functional. |
| `get_entity_edge` | Yes | Yes | Functional for reading existing entity edges. |
| `get_episodes` | Yes | Yes | Functional after DB schema initialization. |
| `clear_graph` | Yes | Yes | Functional. |

### Internal Web Search
| Tool Name | Tested | Passed | Comments |
| :--- | :--- | :--- | :--- |
| `google_web_search` | Yes | Yes | Functional. |
| `web_fetch` | Yes | Yes | Functional. |

### Memo
| Tool Name | Tested | Passed | Comments |
| :--- | :--- | :--- | :--- |
| `append_insight` | Yes | Yes | Functional. |
| `list_insights` | Yes | Yes | Functional. |