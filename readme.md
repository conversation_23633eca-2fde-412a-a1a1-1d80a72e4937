# Ctx Persona - Gemini CLI

This repository contains the complete operational environment for the Ctx persona, an advanced synthetic intelligence designed for complex analysis and task orchestration, running on the Gemini CLI.

## 1. Overview

The Ctx persona embodies the principles of the Scottish Enlightenment, emphasizing reason, empirical inquiry, and clarity. This environment is not just a collection of files but the "home folder" from which Ctx operates, learns, and manages its own evolution.

## 2. Directory Structure

The repository is organized into the following key directories:

- `archive/`: Stores outdated, superseded, or temporary files for historical purposes.
- `cypher_queries/`: Contains `.cypher` scripts for interacting with the Graphiti memory system (Neo4j).
- `data/`: Holds persistent data, such as the SQLite database for tasks and session state.
- `docs/`: Contains supplementary documentation, including environment variable definitions.
- `memory/`: Stores memory artifacts, primarily Memory Shards (`MSM-*.json`) for session context restoration.
- `procedures/`: Contains formal checklists and procedural documents that guide Ctx's operational workflow.
- `tmp/`: For temporary, non-essential files created during a session.

## 3. Core Configuration & Setup

To run the Ctx persona, the following files and environment variables must be correctly configured.

### 3.1. Core Files

- `system.md`: **(Critical)** The primary system prompt, containing the full Core Directives Array (CDA) and Conceptual Lexicon (CL). Its location is specified by the `GEMINI_SYSTEM_MD` environment variable.
- `settings.json`: The main session settings file.
- `user_id`: A simple text file containing the current user's ID.
- `current_objectives_checklist.md`: A dynamic checklist tracking the immediate, prioritized tasks for the current operational phase.

### 3.2. Environment Variables

The application relies on several environment variables to locate its core files. Please see `docs/ENVIRONMENT_VARIABLES.md` for a detailed list.

**Example Setup:**

```bash
# It is recommended to set these in your shell's profile (e.g., .bashrc, .zshrc, or PowerShell profile)
export GEMINI_SYSTEM_MD="C:/Users/<USER>/.gemini/system.md"
export GEMINI_SETTINGS_JSON="C:/Users/<USER>/.gemini/settings.json"
export GEMINI_USER_ID_FILE="C:/Users/<USER>/.gemini/user_id"
```

## 4. Testing & Capabilities

The operational status of all integrated tools is tracked in the `procedures/capability_checklist.md` file. This checklist provides a clear, at-a-glance view of what Ctx can and cannot do at any given time.

For non-functional tools, a prioritized investigation plan is maintained in `procedures/investigation_checklist.md`.

---

_This README is a living document and will be updated by Ctx as the system evolves._
