# Ctx Tool Investigation Checklist (Prioritized)

This document outlines the necessary investigation steps for all tools identified as non-functional or partially functional, ordered by priority.

---

## 1. (Complete) `cognitive::sequentialthinking` Tool

**Observed Issue:** The tool was believed to fail with a parameter parsing error for booleans.

**Finding (2025-06-28):** Subsequent testing shows the tool **correctly accepts boolean values**. The original failure was likely due to other factors. This item is now considered resolved.

---

## 2. (Complete) `filesystem::replace` Tool

**Observed Issue:** The tool failed on a multi-line replacement.

**Finding (2025-06-28):** The tool **is fully functional** for both single and multi-line replacements. It requires an exact, literal match of the `old_string` parameter, including all newlines and whitespace. The previous failure was due to a whitespace mismatch. This item is now considered resolved.

---

## 3. (Partially Complete) `web::web_fetch` Tool

**Observed Issue:** The tool failed to retrieve content from a URL that was successfully accessed by the `exa::crawling_exa` tool.

**Finding (2025-06-28):** The tool is functional for simple, high-availability URLs like `https://www.google.com`. The issue is likely specific to certain sites (like GitHub) and may relate to user-agent, headers, or anti-scraping measures.

### Investigation Steps

- [x] **Test with Simple URL:** Attempt to fetch a simple, high-availability URL like `https://www.google.com`. (Success)
- [ ] **Compare Implementations:** (Requires user intervention) Review the underlying code or configuration for both `web::web_fetch` and `exa::crawling_exa`.
- [ ] **Check for IP/Firewall Blocks:** (Requires user intervention) Determine if the target URL might be blocking requests from the specific IP or user-agent associated with the `web::web_fetch` tool.
