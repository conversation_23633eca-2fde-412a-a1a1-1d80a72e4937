// Main JavaScript for .gemini/www website
// Handles manifest loading, document listing, search, and navigation

class DocumentManager {
    constructor() {
        this.documents = [];
        this.filteredDocuments = [];
        this.currentSort = 'date-desc';
        this.searchTerm = '';
        this.init();
    }

    async init() {
        try {
            await this.loadManifest();
            await this.loadDocumentMetadata();
            this.setupEventListeners();
            this.renderDocuments();
        } catch (error) {
            console.error('Failed to initialize DocumentManager:', error);
            this.showError('Failed to load documents. Please check the manifest.json file.');
        }
    }

    async loadManifest() {
        try {
            const response = await fetch('./manifest.json');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const manifest = await response.json();
            
            if (!manifest.documents || !Array.isArray(manifest.documents)) {
                throw new Error('Invalid manifest format: documents array not found');
            }
            
            this.documents = manifest.documents.filter(doc => !doc.toBeArchived);
            this.filteredDocuments = [...this.documents];
            
            console.log(`Loaded ${this.documents.length} documents from manifest`);
        } catch (error) {
            console.error('Error loading manifest:', error);
            throw error;
        }
    }

    async loadDocumentMetadata() {
        // For each document, try to extract metadata from the markdown file
        const metadataPromises = this.documents.map(async (doc) => {
            try {
                const response = await fetch(`./docs/${doc.fileName}`);
                if (response.ok) {
                    const content = await response.text();
                    const metadata = this.extractMetadata(content, doc.fileName);
                    return { ...doc, ...metadata };
                }
            } catch (error) {
                console.warn(`Could not load metadata for ${doc.fileName}:`, error);
            }
            
            // Fallback metadata
            return {
                ...doc,
                title: this.generateTitleFromFileName(doc.fileName),
                description: 'No description available',
                date: this.extractDateFromFileName(doc.fileName) || new Date().toISOString().split('T')[0],
                tags: []
            };
        });

        this.documents = await Promise.all(metadataPromises);
        this.filteredDocuments = [...this.documents];
    }

    extractMetadata(content, fileName) {
        const lines = content.split('\n');
        let title = '';
        let description = '';
        let status = '';
        
        // Extract title from first heading
        for (const line of lines) {
            if (line.startsWith('# ')) {
                title = line.substring(2).trim();
                break;
            }
        }
        
        // Extract status if present
        const statusMatch = content.match(/\*\*STATUS:\*\*\s*(.+)/i);
        if (statusMatch) {
            status = statusMatch[1].trim();
        }
        
        // Extract description from first paragraph after title
        let foundTitle = false;
        for (const line of lines) {
            if (line.startsWith('# ')) {
                foundTitle = true;
                continue;
            }
            if (foundTitle && line.trim() && !line.startsWith('**') && !line.startsWith('---')) {
                description = line.trim();
                break;
            }
        }
        
        return {
            title: title || this.generateTitleFromFileName(fileName),
            description: description || 'No description available',
            date: this.extractDateFromFileName(fileName) || new Date().toISOString().split('T')[0],
            status: status,
            tags: this.extractTagsFromContent(content)
        };
    }

    generateTitleFromFileName(fileName) {
        return fileName
            .replace(/\.md$/, '')
            .replace(/checklist-\d{8}-\d{6}-/, '')
            .replace(/-/g, ' ')
            .replace(/\b\w/g, l => l.toUpperCase());
    }

    extractDateFromFileName(fileName) {
        const dateMatch = fileName.match(/(\d{4})(\d{2})(\d{2})/);
        if (dateMatch) {
            return `${dateMatch[1]}-${dateMatch[2]}-${dateMatch[3]}`;
        }
        return null;
    }

    extractTagsFromContent(content) {
        const tags = [];
        
        // Look for common patterns that might indicate tags
        if (content.includes('checklist')) tags.push('checklist');
        if (content.includes('neo4j')) tags.push('neo4j');
        if (content.includes('migration')) tags.push('migration');
        if (content.includes('refactor')) tags.push('refactor');
        if (content.includes('testing')) tags.push('testing');
        if (content.includes('STATUS: PENDING')) tags.push('pending');
        if (content.includes('STATUS: COMPLETE')) tags.push('complete');
        
        return tags;
    }

    setupEventListeners() {
        // Search functionality
        const searchBox = document.querySelector('.search-box');
        if (searchBox) {
            searchBox.addEventListener('input', (e) => {
                this.searchTerm = e.target.value.toLowerCase();
                this.filterAndSort();
            });
        }

        // Sort buttons
        document.querySelectorAll('.sort-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.currentSort = e.target.dataset.sort;
                document.querySelectorAll('.sort-button').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.filterAndSort();
            });
        });
    }

    filterAndSort() {
        // Filter by search term
        this.filteredDocuments = this.documents.filter(doc => {
            if (!this.searchTerm) return true;
            
            return (
                doc.title.toLowerCase().includes(this.searchTerm) ||
                doc.description.toLowerCase().includes(this.searchTerm) ||
                doc.fileName.toLowerCase().includes(this.searchTerm) ||
                (doc.tags && doc.tags.some(tag => tag.toLowerCase().includes(this.searchTerm)))
            );
        });

        // Sort documents
        this.filteredDocuments.sort((a, b) => {
            switch (this.currentSort) {
                case 'date-desc':
                    return new Date(b.date) - new Date(a.date);
                case 'date-asc':
                    return new Date(a.date) - new Date(b.date);
                case 'title-asc':
                    return a.title.localeCompare(b.title);
                case 'title-desc':
                    return b.title.localeCompare(a.title);
                default:
                    return 0;
            }
        });

        this.renderDocuments();
    }

    renderDocuments() {
        const container = document.getElementById('card-container');
        if (!container) {
            console.error('Card container not found');
            return;
        }

        if (this.filteredDocuments.length === 0) {
            container.innerHTML = `
                <div class="no-results">
                    <p>No documents found${this.searchTerm ? ` for "${this.searchTerm}"` : ''}.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.filteredDocuments.map(doc => this.createDocumentCard(doc)).join('');
    }

    createDocumentCard(doc) {
        const tags = doc.tags && doc.tags.length > 0 
            ? doc.tags.map(tag => `<span class="tag">${tag}</span>`).join('')
            : '';

        return `
            <a href="viewer.html?doc=${encodeURIComponent(doc.fileName)}" class="document-card">
                <h3 class="card-title">${this.escapeHtml(doc.title)}</h3>
                <p class="card-description">${this.escapeHtml(doc.description)}</p>
                <div class="card-meta">
                    <span class="card-date">${doc.date}</span>
                    <div class="card-tags">${tags}</div>
                </div>
            </a>
        `;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showError(message) {
        const container = document.getElementById('card-container');
        if (container) {
            container.innerHTML = `
                <div class="error-message">
                    <p>⚠️ ${message}</p>
                </div>
            `;
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new DocumentManager();
});
