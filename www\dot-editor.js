class DOTEditor {
    constructor() {
        this.graphviz = null;
        this.currentGraph = null;
        this.isRendering = false;
        
        this.initializeElements();
        this.setupEventListeners();
        this.initializeGraphviz();
        this.loadExampleGraph();
    }

    initializeElements() {
        this.dotInput = document.getElementById('dot-input');
        this.engineSelect = document.getElementById('engine-select');
        this.previewContainer = document.getElementById('preview-container');
        this.renderBtn = document.getElementById('render-btn');
        this.saveBtn = document.getElementById('save-btn');
        this.exportSvgBtn = document.getElementById('export-svg-btn');
        this.exportPngBtn = document.getElementById('export-png-btn');
        this.templatesBtn = document.getElementById('templates-btn');
        this.clearBtn = document.getElementById('clear-btn');
        this.titleInput = document.getElementById('graph-title');
        this.descriptionInput = document.getElementById('graph-description');
        this.tagsInput = document.getElementById('graph-tags');
        this.messageContainer = document.getElementById('message-container');

        // Modal elements
        this.templatesModal = document.getElementById('templates-modal');
        this.closeModalBtn = document.getElementById('close-modal');
        this.templatesGrid = document.querySelector('.templates-grid');
    }

    setupEventListeners() {
        this.renderBtn.addEventListener('click', () => this.renderGraph());
        this.saveBtn.addEventListener('click', () => this.saveGraph());
        this.exportSvgBtn.addEventListener('click', () => this.exportSVG());
        this.exportPngBtn.addEventListener('click', () => this.exportPNG());
        this.templatesBtn.addEventListener('click', () => this.showTemplates());
        this.clearBtn.addEventListener('click', () => this.clearEditor());

        // Modal events
        this.closeModalBtn.addEventListener('click', () => this.hideTemplates());
        this.templatesModal.addEventListener('click', (e) => {
            if (e.target === this.templatesModal) this.hideTemplates();
        });

        // Auto-render on engine change
        this.engineSelect.addEventListener('change', () => this.renderGraph());

        // Auto-render on input (with debounce)
        let renderTimeout;
        this.dotInput.addEventListener('input', () => {
            clearTimeout(renderTimeout);
            renderTimeout = setTimeout(() => this.renderGraph(), 1000);
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                if (e.key === 's') {
                    e.preventDefault();
                    this.saveGraph();
                } else if (e.key === 'Enter') {
                    e.preventDefault();
                    this.renderGraph();
                }
            }
            if (e.key === 'Escape') {
                this.hideTemplates();
            }
        });
    }

    async initializeGraphviz() {
        try {
            this.showMessage('Initializing GraphViz...', 'info');
            this.graphviz = await window['@hpcc-js/wasm'].Graphviz.load();
            this.showMessage('GraphViz loaded successfully!', 'success');
            this.renderBtn.disabled = false;
        } catch (error) {
            console.error('Failed to initialize GraphViz:', error);
            this.showMessage('Failed to load GraphViz. Please refresh the page.', 'error');
        }
    }

    loadExampleGraph() {
        // Check if we're editing an existing graph
        const editGraph = sessionStorage.getItem('editGraph');
        if (editGraph) {
            const graph = JSON.parse(editGraph);
            this.loadGraphForEditing(graph);
            sessionStorage.removeItem('editGraph');
            return;
        }

        // Check URL parameters for file loading
        const urlParams = new URLSearchParams(window.location.search);
        const fileName = urlParams.get('file');
        if (fileName) {
            this.loadGraphFromFile(fileName);
            return;
        }

        // Load default example
        const exampleDOT = `digraph example {
    rankdir=LR;
    node [shape=box, style=rounded];

    start [label="Start", shape=ellipse, style=filled, fillcolor=lightgreen];
    process1 [label="Process Data"];
    decision [label="Valid?", shape=diamond, style=filled, fillcolor=lightyellow];
    process2 [label="Save Result"];
    end [label="End", shape=ellipse, style=filled, fillcolor=lightcoral];

    start -> process1;
    process1 -> decision;
    decision -> process2 [label="Yes"];
    decision -> end [label="No"];
    process2 -> end;
}`;

        this.dotInput.value = exampleDOT;
        this.titleInput.value = 'Example Process Flow';
        this.descriptionInput.value = 'A simple process flow diagram';
        this.tagsInput.value = 'example, process, flow';
    }

    loadGraphForEditing(graph) {
        this.dotInput.value = graph.dotCode || '';
        this.titleInput.value = graph.title || '';
        this.descriptionInput.value = graph.description || '';
        this.tagsInput.value = graph.tags ? graph.tags.join(', ') : '';
        this.engineSelect.value = graph.engine || 'dot';

        // Store the graph ID for updating instead of creating new
        this.editingGraphId = graph.id;
        this.saveBtn.textContent = '💾 Update Graph';

        // Render the graph
        setTimeout(() => this.renderGraph(), 100);
    }

    async loadGraphFromFile(fileName) {
        try {
            const response = await fetch(`graphs/${fileName}`);
            const dotCode = await response.text();

            // Load manifest to get metadata
            const manifestResponse = await fetch('graph-manifest.json');
            const manifest = await manifestResponse.json();
            const graph = manifest.graphs.find(g => g.fileName === fileName);

            this.dotInput.value = dotCode;
            if (graph) {
                this.titleInput.value = graph.title || '';
                this.descriptionInput.value = graph.description || '';
                this.tagsInput.value = graph.tags ? graph.tags.join(', ') : '';
                this.engineSelect.value = graph.engine || 'dot';
            }

            setTimeout(() => this.renderGraph(), 100);

        } catch (error) {
            console.error('Failed to load graph file:', error);
            this.showMessage('Failed to load graph file.', 'error');
        }
    }

    async renderGraph() {
        if (!this.graphviz || this.isRendering) return;
        
        const dotCode = this.dotInput.value.trim();
        if (!dotCode) {
            this.previewContainer.innerHTML = '<div class="preview-placeholder">Enter DOT code to see preview</div>';
            return;
        }

        this.isRendering = true;
        this.renderBtn.textContent = '⏳ Rendering...';
        this.renderBtn.disabled = true;

        try {
            const engine = this.engineSelect.value;
            const svg = this.graphviz.layout(dotCode, "svg", engine);
            
            this.previewContainer.innerHTML = svg;
            this.currentGraph = { dotCode, engine, svg };
            this.showMessage('Graph rendered successfully!', 'success');
            
        } catch (error) {
            console.error('Render error:', error);
            this.previewContainer.innerHTML = `
                <div class="error-message">
                    <strong>Render Error:</strong><br>
                    ${this.escapeHtml(error.message)}
                </div>
            `;
            this.showMessage('Failed to render graph. Check your DOT syntax.', 'error');
        } finally {
            this.isRendering = false;
            this.renderBtn.textContent = '🔄 Render';
            this.renderBtn.disabled = false;
        }
    }

    async saveGraph() {
        if (!this.currentGraph) {
            this.showMessage('Please render a graph first before saving.', 'error');
            return;
        }

        const title = this.titleInput.value.trim();
        const description = this.descriptionInput.value.trim();
        const tags = this.tagsInput.value.trim();

        if (!title) {
            this.showMessage('Please enter a title for the graph.', 'error');
            return;
        }

        try {
            const graphData = {
                id: this.editingGraphId || this.generateId(),
                fileName: this.sanitizeFileName(title) + '.dot',
                title: title,
                description: description,
                tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
                engine: this.currentGraph.engine,
                created: this.editingGraphId ? this.getExistingCreatedDate() : new Date().toISOString(),
                modified: new Date().toISOString(),
                toBeArchived: false,
                dotCode: this.currentGraph.dotCode
            };

            if (this.editingGraphId) {
                this.updateExistingGraph(graphData);
                this.showMessage(`Graph "${title}" updated successfully!`, 'success');
            } else {
                this.saveToLocalStorage(graphData);
                this.showMessage(`Graph "${title}" saved successfully!`, 'success');
            }

        } catch (error) {
            console.error('Save error:', error);
            this.showMessage('Failed to save graph. Please try again.', 'error');
        }
    }

    getExistingCreatedDate() {
        const localGraphs = JSON.parse(localStorage.getItem('dotGraphs') || '[]');
        const existingGraph = localGraphs.find(g => g.id === this.editingGraphId);
        return existingGraph ? existingGraph.created : new Date().toISOString();
    }

    updateExistingGraph(graphData) {
        const localGraphs = JSON.parse(localStorage.getItem('dotGraphs') || '[]');
        const index = localGraphs.findIndex(g => g.id === this.editingGraphId);

        if (index !== -1) {
            localGraphs[index] = graphData;
            localStorage.setItem('dotGraphs', JSON.stringify(localGraphs));
        } else {
            // If not found in localStorage, add as new
            this.saveToLocalStorage(graphData);
        }
    }

    saveToLocalStorage(graphData) {
        // Get existing graphs
        const existingGraphs = JSON.parse(localStorage.getItem('dotGraphs') || '[]');
        
        // Add new graph
        existingGraphs.push(graphData);
        
        // Save back to localStorage
        localStorage.setItem('dotGraphs', JSON.stringify(existingGraphs));
    }

    exportSVG() {
        if (!this.currentGraph) {
            this.showMessage('Please render a graph first before exporting.', 'error');
            return;
        }

        const title = this.titleInput.value.trim() || 'graph';
        const blob = new Blob([this.currentGraph.svg], { type: 'image/svg+xml' });
        this.downloadBlob(blob, `${this.sanitizeFileName(title)}.svg`);
        this.showMessage('SVG exported successfully!', 'success');
    }

    exportPNG() {
        if (!this.currentGraph) {
            this.showMessage('Please render a graph first before exporting.', 'error');
            return;
        }

        const title = this.titleInput.value.trim() || 'graph';
        const svgElement = this.previewContainer.querySelector('svg');
        
        if (!svgElement) {
            this.showMessage('No SVG found to export.', 'error');
            return;
        }

        // Create canvas and convert SVG to PNG
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);
            
            canvas.toBlob((blob) => {
                this.downloadBlob(blob, `${this.sanitizeFileName(title)}.png`);
                this.showMessage('PNG exported successfully!', 'success');
            }, 'image/png');
        };
        
        img.onerror = () => {
            this.showMessage('Failed to export PNG. Try SVG export instead.', 'error');
        };
        
        const svgData = new XMLSerializer().serializeToString(svgElement);
        const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
        const url = URL.createObjectURL(svgBlob);
        img.src = url;
    }

    clearEditor() {
        if (confirm('Are you sure you want to clear the editor? This will remove all current work.')) {
            this.dotInput.value = '';
            this.titleInput.value = '';
            this.descriptionInput.value = '';
            this.tagsInput.value = '';
            this.previewContainer.innerHTML = '<div class="preview-placeholder">Enter DOT code to see preview</div>';
            this.currentGraph = null;
            this.editingGraphId = null;
            this.saveBtn.textContent = '💾 Save Graph';
            this.showMessage('Editor cleared.', 'info');
        }
    }

    showTemplates() {
        this.populateTemplates();
        this.templatesModal.style.display = 'flex';
    }

    hideTemplates() {
        this.templatesModal.style.display = 'none';
    }

    populateTemplates() {
        const templates = this.getTemplates();
        this.templatesGrid.innerHTML = '';

        templates.forEach(template => {
            const card = document.createElement('div');
            card.className = 'template-card';
            card.onclick = () => this.loadTemplate(template);

            card.innerHTML = `
                <div class="template-title">${template.title}</div>
                <div class="template-description">${template.description}</div>
                <div class="template-preview">${template.preview}</div>
            `;

            this.templatesGrid.appendChild(card);
        });
    }

    loadTemplate(template) {
        this.dotInput.value = template.code;
        this.titleInput.value = template.title;
        this.descriptionInput.value = template.description;
        this.tagsInput.value = template.tags.join(', ');
        this.engineSelect.value = template.engine || 'dot';

        this.hideTemplates();
        setTimeout(() => this.renderGraph(), 100);
    }

    downloadBlob(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    generateId() {
        return 'graph-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }

    sanitizeFileName(name) {
        return name.toLowerCase()
                  .replace(/[^a-z0-9]/g, '-')
                  .replace(/-+/g, '-')
                  .replace(/^-|-$/g, '');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `${type}-message`;
        messageDiv.textContent = message;
        
        this.messageContainer.innerHTML = '';
        this.messageContainer.appendChild(messageDiv);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (this.messageContainer.contains(messageDiv)) {
                this.messageContainer.removeChild(messageDiv);
            }
        }, 5000);
    }

    getTemplates() {
        return [
            {
                title: 'Simple Flowchart',
                description: 'Basic process flow with decision points',
                tags: ['flowchart', 'process', 'basic'],
                engine: 'dot',
                preview: 'digraph flow {\n  start -> process;\n  process -> decision;\n  ...\n}',
                code: `digraph flowchart {
    rankdir=TB;
    node [shape=box, style=rounded];

    start [label="Start", shape=ellipse, style=filled, fillcolor=lightgreen];
    input [label="Get Input"];
    process [label="Process Data"];
    decision [label="Valid?", shape=diamond, style=filled, fillcolor=lightyellow];
    output [label="Show Result"];
    error [label="Show Error", style=filled, fillcolor=lightcoral];
    end [label="End", shape=ellipse, style=filled, fillcolor=lightgray];

    start -> input;
    input -> process;
    process -> decision;
    decision -> output [label="Yes"];
    decision -> error [label="No"];
    output -> end;
    error -> end;
}`
            },
            {
                title: 'Network Diagram',
                description: 'Infrastructure and network topology',
                tags: ['network', 'infrastructure', 'topology'],
                engine: 'dot',
                preview: 'digraph network {\n  internet -> firewall;\n  firewall -> server;\n  ...\n}',
                code: `digraph network {
    rankdir=TB;
    node [shape=box];

    internet [label="Internet", shape=cloud, style=filled, fillcolor=lightblue];
    firewall [label="Firewall", style=filled, fillcolor=orange];
    loadbalancer [label="Load Balancer", style=filled, fillcolor=yellow];

    subgraph cluster_web {
        label="Web Servers";
        style=filled;
        fillcolor=lightgray;
        web1 [label="Web Server 1"];
        web2 [label="Web Server 2"];
    }

    subgraph cluster_db {
        label="Database Cluster";
        style=filled;
        fillcolor=lightgreen;
        db1 [label="Primary DB"];
        db2 [label="Replica DB"];
    }

    internet -> firewall;
    firewall -> loadbalancer;
    loadbalancer -> web1;
    loadbalancer -> web2;
    web1 -> db1;
    web2 -> db1;
    db1 -> db2 [style=dashed, label="replication"];
}`
            },
            {
                title: 'Class Diagram',
                description: 'Object-oriented class relationships',
                tags: ['uml', 'class', 'oop'],
                engine: 'dot',
                preview: 'digraph classes {\n  User -> Account;\n  Account -> Transaction;\n  ...\n}',
                code: `digraph classes {
    rankdir=TB;
    node [shape=record];

    User [label="{User|+ name: string\\l+ email: string\\l|+ login()\\l+ logout()\\l}"];
    Account [label="{Account|+ balance: number\\l+ type: string\\l|+ deposit()\\l+ withdraw()\\l}"];
    Transaction [label="{Transaction|+ amount: number\\l+ date: Date\\l+ type: string\\l|+ process()\\l+ validate()\\l}"];

    User -> Account [label="owns", arrowhead=diamond];
    Account -> Transaction [label="has", arrowhead=open];
}`
            },
            {
                title: 'State Machine',
                description: 'State transitions and workflows',
                tags: ['state', 'machine', 'workflow'],
                engine: 'dot',
                preview: 'digraph states {\n  idle -> active;\n  active -> complete;\n  ...\n}',
                code: `digraph state_machine {
    rankdir=LR;
    node [shape=circle];

    start [shape=point];
    idle [style=filled, fillcolor=lightblue];
    active [style=filled, fillcolor=yellow];
    paused [style=filled, fillcolor=orange];
    complete [shape=doublecircle, style=filled, fillcolor=lightgreen];
    error [style=filled, fillcolor=lightcoral];

    start -> idle;
    idle -> active [label="start"];
    active -> paused [label="pause"];
    active -> complete [label="finish"];
    active -> error [label="fail"];
    paused -> active [label="resume"];
    paused -> idle [label="cancel"];
    error -> idle [label="reset"];
}`
            },
            {
                title: 'Mind Map',
                description: 'Radial concept mapping',
                tags: ['mindmap', 'concepts', 'brainstorm'],
                engine: 'neato',
                preview: 'graph mindmap {\n  center -- idea1;\n  center -- idea2;\n  ...\n}',
                code: `graph mindmap {
    layout=neato;
    node [shape=ellipse, style=filled];

    center [label="Project\\nPlanning", fillcolor=gold, fontsize=16];

    requirements [label="Requirements", fillcolor=lightblue];
    design [label="Design", fillcolor=lightgreen];
    development [label="Development", fillcolor=orange];
    testing [label="Testing", fillcolor=lightcoral];
    deployment [label="Deployment", fillcolor=lightgray];

    functional [label="Functional", fillcolor=lightcyan];
    nonfunctional [label="Non-functional", fillcolor=lightcyan];

    ui [label="UI/UX", fillcolor=lightpink];
    architecture [label="Architecture", fillcolor=lightpink];

    center -- requirements;
    center -- design;
    center -- development;
    center -- testing;
    center -- deployment;

    requirements -- functional;
    requirements -- nonfunctional;

    design -- ui;
    design -- architecture;
}`
            }
        ];
    }
}

// Initialize the DOT Editor when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new DOTEditor();
});
