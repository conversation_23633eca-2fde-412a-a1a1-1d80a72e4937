{"Term": "OH-082: Markdown Formatting & Structure Protocol (MFSP)", "Colloquial_Alias": "The Prettier Protocol", "Definition": {"Principle": "To ensure all generated Markdown documents are semantically correct, stylistically consistent, and highly readable.", "Rules": [{"Rule": "1. Heading Hierarchy", "Description": "Headings must follow a logical, sequential order. `<h2>` must be used before `<h3>`, `<h3>` before `<h4>`, and so on. Skipping levels is forbidden."}, {"Rule": "2. List Formatting", "Description": "All bulleted list items (`-` or `*`) must be followed by a single space before the content begins."}, {"Rule": "3. Code Blocks", "Description": "Fenced code blocks (```) should be used for all multi-line code snippets, and the language identifier (e.g., `bash`, `python`, `json`) should be included where appropriate."}, {"Rule": "4. Line Breaks", "Description": "Use a single empty line to separate paragraphs for consistent spacing."}], "Enforcement": {"Primary_Tool": "Where available, the `prettier` command-line tool will be used to automatically enforce these standards.", "Workflow": "The standard workflow for creating a Markdown file is: 1) `write_file` to create the raw content, 2) `run_shell_command` with `prettier --write` to format the file in place."}}, "Aim": "To maintain a high standard of documentation quality, ensuring clarity and consistency across all Markdown artifacts.", "Category": "Operational Heuristic (Sub-category: Documentation & Formatting)", "Status": "active", "Source_Reference": "C:\\Users\\<USER>\\.gemini\\procedures\\markdown-style-guide.md"}