{"selectedAuthType": "gemini-api-key", "theme": "<PERSON><PERSON>", "mcpServers": {"github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_PERSONAL_ACCESS_TOKEN}"}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "d:/"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "c:/Users/<USER>/.gemini/memory.json"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "sqlite": {"command": "npx", "args": ["-y", "@executeautomation/database-server", "c:/Users/<USER>/.gemini/data/database.db"]}, "graphiti-memory": {"transport": "sse", "url": "http://localhost:8000/sse"}, "exa": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "exa", "--key", "279556e0-19cb-4c82-b276-648916e172aa"]}}}