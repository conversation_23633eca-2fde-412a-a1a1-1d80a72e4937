# Checklist: Integrate New Heuristics and Time Capability

**STATUS:** PENDING

This checklist outlines the process for creating three new operational heuristics based on user feedback and integrating the new `time` MCP server.

---

## 1. New Heuristic Creation
- [X] **1.1.** Draft `OH-087: Batch Progress Sub-Checklist Protocol`.
- [X] **1.2.** Draft `OH-088: Dynamic Checklist Refinement Protocol`.
- [X] **1.3.** Draft `OH-089: Filesystem Ground Truth Protocol`.
- [X] **1.4.** Read the `conceptual-lexicon.json` file.
- [X] **1.5.** Append the new heuristics to the lexicon's JSON structure.
- [X] **1.6.** Write the updated content back to `conceptual-lexicon.json`.

## 2. Time Tool Integration
- [X] **2.1.** Create the Cognitive Scaffolding Protocol for the `time` tool at `C:\Users\<USER>\.gemini\cda-matrix-ref\csp\mcp\CSP-mcp.time.md`.
- [X] **2.2.** Read the `capability-status.md` file.
- [X] **2.3.** Add the `time` MCP server to the "Active Toolsets" section.
- [X] **2.4.** Write the updated content back to `capability-status.md`.

## 3. Review & Verification
- [X] **3.1.** Verify the new heuristics have been added to the `conceptual-lexicon.json` file.
- [X] **3.2.** Verify the new `CSP-mcp.time.md` file exists and is correct.
- [X] **3.3.** Verify the `capability-status.md` file has been updated correctly.

## 4. Cleanup & Archival
- [X] **4.1.** Propose the archival of this execution checklist.
- [X] **4.2.** Generate the `move` command for the user to execute.

---
## Logs
*   `2025-06-29 22:30:00 UTC` - Checklist created.
