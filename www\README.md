# Ctx | Gemini-CLI Dashboard

A modern, responsive website for viewing and managing markdown documents with a clean, user-friendly interface.

## Features

- **📋 Document Listing**: Browse all available documents with search and filtering
- **🔍 Search & Sort**: Real-time search with multiple sorting options
- **📖 Document Viewer**: Clean markdown rendering with table support and syntax highlighting
- **📱 Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **🌙 Dark/Light Theme**: Automatic theme switching based on system preference
- **⚡ Fast Loading**: Optimized performance with CDN resources and efficient code

## Quick Start

1. **Start the server** (http-server should already be running):
   ```bash
   cd www
   http-server
   ```

2. **Open in browser**: Navigate to `http://localhost:8080` (or the port shown by http-server)

3. **Browse documents**: Click on any document card to view its content

## File Structure

```
www/
├── index.html          # Landing page with document listing
├── viewer.html         # Document viewer page
├── style.css          # Main stylesheet with responsive design
├── script.js          # Landing page JavaScript
├── viewer.js          # Document viewer JavaScript
├── manifest.json      # Document metadata and configuration
├── docs/              # Markdown documents folder
│   ├── document1.md
│   ├── document2.md
│   └── ...
└── README.md          # This file
```

## Manifest.json Format

The `manifest.json` file controls which documents are displayed and their metadata:

```json
{
  "documents": [
    {
      "fileName": "example-document.md",
      "toBeArchived": false
    },
    {
      "fileName": "archived-document.md",
      "toBeArchived": true
    }
  ]
}
```

### Schema

- **fileName** (string, required): Name of the markdown file in the `docs/` folder
- **toBeArchived** (boolean, required): If `true`, document will be hidden from the listing

### Third-Party Management

The manifest system is designed for easy third-party management:

1. **Simple Schema**: Only two required fields make it easy to generate programmatically
2. **Archive Control**: Use `toBeArchived` to hide documents without deleting files
3. **Automatic Metadata**: Title, description, date, and tags are extracted from markdown content
4. **Error Handling**: Graceful fallbacks if documents are missing or malformed

## Document Metadata Extraction

The system automatically extracts metadata from markdown files:

- **Title**: First `# Heading` in the document
- **Description**: First paragraph after the title
- **Date**: Extracted from filename pattern `YYYYMMDD` or current date as fallback
- **Status**: Extracted from `**STATUS:** value` pattern
- **Tags**: Auto-generated based on content keywords

## Supported Markdown Features

- **Headers** (H1-H6)
- **Paragraphs** and text formatting
- **Lists** (ordered and unordered)
- **Tables** (full support)
- **Code blocks** with syntax highlighting
- **Blockquotes**
- **Links** (auto-linking enabled)
- **HTML** (limited safe HTML support)

## Browser Support

- **Chrome** 90+
- **Firefox** 88+
- **Safari** 14+
- **Edge** 90+

## Development

### Adding New Documents

1. Add markdown file to `docs/` folder
2. Update `manifest.json` to include the new file:
   ```json
   {
     "fileName": "new-document.md",
     "toBeArchived": false
   }
   ```
3. Refresh the website - changes appear immediately

### Customizing Styles

The website uses [Open Props](https://open-props.style/) for consistent design tokens. Key CSS custom properties:

```css
:root {
  --primary-color: var(--blue-6);
  --text-color: var(--gray-9);
  --bg-color: var(--gray-0);
  --card-bg: var(--gray-1);
  /* ... */
}
```

### Performance Optimization

- **CDN Resources**: External libraries loaded from CDN
- **Efficient CSS**: Uses Open Props for minimal custom CSS
- **Lazy Loading**: Documents loaded only when viewed
- **Caching**: Browser caching enabled for static assets

## Troubleshooting

### Documents Not Loading
- Check that `manifest.json` is valid JSON
- Verify document files exist in `docs/` folder
- Check browser console for error messages

### Search Not Working
- Ensure JavaScript is enabled
- Check for console errors
- Verify manifest loaded successfully

### Styling Issues
- Check that `style.css` is loading
- Verify Open Props CDN is accessible
- Test in different browsers

## Technical Details

- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **Markdown Parser**: markdown-it with table support
- **Syntax Highlighting**: highlight.js
- **CSS Framework**: Open Props
- **Server**: Any static file server (http-server recommended)

## License

This project is part of the Ctx | Gemini-CLI system.
