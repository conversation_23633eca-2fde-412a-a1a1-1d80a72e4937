class DOTGallery {
    constructor() {
        this.graphviz = null;
        this.graphs = [];
        this.filteredGraphs = [];
        this.allTags = new Set();
        
        this.initializeElements();
        this.setupEventListeners();
        this.initializeGraphviz();
        this.loadGraphs();
    }

    initializeElements() {
        this.loadingState = document.getElementById('loading-state');
        this.graphsGrid = document.getElementById('graphs-grid');
        this.emptyState = document.getElementById('empty-state');
        this.searchInput = document.getElementById('search-input');
        this.filterSelect = document.getElementById('filter-select');
    }

    setupEventListeners() {
        this.searchInput.addEventListener('input', () => this.filterGraphs());
        this.filterSelect.addEventListener('change', () => this.filterGraphs());
    }

    async initializeGraphviz() {
        try {
            this.graphviz = await window['@hpcc-js/wasm'].Graphviz.load();
        } catch (error) {
            console.error('Failed to initialize GraphViz:', error);
        }
    }

    async loadGraphs() {
        try {
            // Load from manifest file
            const manifestResponse = await fetch('graph-manifest.json');
            const manifest = await manifestResponse.json();
            
            // Load from localStorage (saved graphs)
            const localGraphs = JSON.parse(localStorage.getItem('dotGraphs') || '[]');
            
            // Combine both sources
            this.graphs = [...manifest.graphs, ...localGraphs];
            
            // Extract all tags for filter dropdown
            this.graphs.forEach(graph => {
                if (graph.tags) {
                    graph.tags.forEach(tag => this.allTags.add(tag));
                }
            });
            
            this.populateTagFilter();
            this.filteredGraphs = [...this.graphs];
            this.renderGraphs();
            
        } catch (error) {
            console.error('Failed to load graphs:', error);
            this.showEmptyState();
        }
    }

    populateTagFilter() {
        // Clear existing options except "All Tags"
        this.filterSelect.innerHTML = '<option value="">All Tags</option>';
        
        // Add tag options
        Array.from(this.allTags).sort().forEach(tag => {
            const option = document.createElement('option');
            option.value = tag;
            option.textContent = tag;
            this.filterSelect.appendChild(option);
        });
    }

    filterGraphs() {
        const searchTerm = this.searchInput.value.toLowerCase();
        const selectedTag = this.filterSelect.value;
        
        this.filteredGraphs = this.graphs.filter(graph => {
            const matchesSearch = !searchTerm || 
                graph.title.toLowerCase().includes(searchTerm) ||
                graph.description.toLowerCase().includes(searchTerm) ||
                (graph.tags && graph.tags.some(tag => tag.toLowerCase().includes(searchTerm)));
            
            const matchesTag = !selectedTag || 
                (graph.tags && graph.tags.includes(selectedTag));
            
            return matchesSearch && matchesTag;
        });
        
        this.renderGraphs();
    }

    async renderGraphs() {
        this.loadingState.style.display = 'none';
        
        if (this.filteredGraphs.length === 0) {
            this.showEmptyState();
            return;
        }
        
        this.emptyState.style.display = 'none';
        this.graphsGrid.style.display = 'grid';
        this.graphsGrid.innerHTML = '';
        
        for (const graph of this.filteredGraphs) {
            const card = await this.createGraphCard(graph);
            this.graphsGrid.appendChild(card);
        }
    }

    async createGraphCard(graph) {
        const card = document.createElement('div');
        card.className = 'graph-card';
        card.onclick = () => this.viewGraph(graph);
        
        // Generate thumbnail
        const thumbnailHtml = await this.generateThumbnail(graph);
        
        // Format date
        const createdDate = new Date(graph.created).toLocaleDateString();
        
        // Create tags HTML
        const tagsHtml = graph.tags ? 
            graph.tags.map(tag => `<span class="tag">${this.escapeHtml(tag)}</span>`).join('') : '';
        
        card.innerHTML = `
            <div class="graph-thumbnail">
                ${thumbnailHtml}
            </div>
            <div class="graph-info">
                <h3 class="graph-title">${this.escapeHtml(graph.title)}</h3>
                <p class="graph-description">${this.escapeHtml(graph.description || 'No description')}</p>
                <div class="graph-meta">
                    <span class="graph-engine">${graph.engine || 'dot'}</span>
                    <span>${createdDate}</span>
                </div>
                <div class="graph-tags">
                    ${tagsHtml}
                </div>
                <div class="graph-actions">
                    <a href="dot-viewer.html?id=${graph.id}" class="action-btn view" onclick="event.stopPropagation()">👁️ View</a>
                    <button class="action-btn" onclick="event.stopPropagation(); dotGallery.editGraph('${graph.id}')">✏️ Edit</button>
                    <button class="action-btn" onclick="event.stopPropagation(); dotGallery.deleteGraph('${graph.id}')">🗑️ Delete</button>
                </div>
            </div>
        `;
        
        return card;
    }

    async generateThumbnail(graph) {
        if (!this.graphviz) {
            return '<div class="thumbnail-placeholder">Loading...</div>';
        }
        
        try {
            let dotCode = graph.dotCode;
            
            // If no dotCode in graph object, try to load from file
            if (!dotCode && graph.fileName) {
                try {
                    const response = await fetch(`graphs/${graph.fileName}`);
                    dotCode = await response.text();
                } catch (error) {
                    console.warn(`Could not load DOT file: ${graph.fileName}`);
                }
            }
            
            if (!dotCode) {
                return '<div class="thumbnail-placeholder">No preview available</div>';
            }
            
            const svg = this.graphviz.layout(dotCode, "svg", graph.engine || "dot");
            return `<div class="thumbnail-svg">${svg}</div>`;
            
        } catch (error) {
            console.warn('Failed to generate thumbnail:', error);
            return '<div class="thumbnail-placeholder">Preview error</div>';
        }
    }

    viewGraph(graph) {
        window.location.href = `dot-viewer.html?id=${graph.id}`;
    }

    editGraph(graphId) {
        // For localStorage graphs, we can edit directly
        const localGraphs = JSON.parse(localStorage.getItem('dotGraphs') || '[]');
        const graph = localGraphs.find(g => g.id === graphId);

        if (graph) {
            // Store graph data for editor to pick up
            sessionStorage.setItem('editGraph', JSON.stringify(graph));
            window.location.href = 'dot-editor.html';
        } else {
            // For manifest graphs, redirect to editor with file parameter
            const manifestGraph = this.graphs.find(g => g.id === graphId);
            if (manifestGraph && manifestGraph.fileName) {
                window.location.href = `dot-editor.html?file=${manifestGraph.fileName}`;
            }
        }
    }

    async deleteGraph(graphId) {
        if (!confirm('Are you sure you want to delete this graph? This action cannot be undone.')) {
            return;
        }
        
        // Only allow deletion of localStorage graphs (user-created)
        const localGraphs = JSON.parse(localStorage.getItem('dotGraphs') || '[]');
        const updatedGraphs = localGraphs.filter(g => g.id !== graphId);
        
        if (localGraphs.length !== updatedGraphs.length) {
            localStorage.setItem('dotGraphs', JSON.stringify(updatedGraphs));
            this.loadGraphs(); // Reload to update display
        } else {
            alert('Cannot delete example graphs. Only user-created graphs can be deleted.');
        }
    }

    showEmptyState() {
        this.loadingState.style.display = 'none';
        this.graphsGrid.style.display = 'none';
        this.emptyState.style.display = 'block';
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize the gallery when the page loads
let dotGallery;
document.addEventListener('DOMContentLoaded', () => {
    dotGallery = new DOTGallery();
});
