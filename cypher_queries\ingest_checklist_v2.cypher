// Ingest the full capability checklist (v2 format with checklist_id)
LOAD CSV WITH HEADERS FROM 'file:///capability_checklist_v2.csv' AS row
MERGE (c:Checklist {checklist_id: row.checklist_id})
MERGE (cat:Category {name: row.category})
MERGE (t:Tool {name: row.item})
MERGE (s:Status {value: row.status})
MERGE (r:Result {value: row.result})
MERGE (com:Comment {text: row.comment})
MERGE (c)-[:HAS_CATEGORY]->(cat)
MERGE (cat)-[:CONTAINS_TOOL]->(t)
MERGE (t)-[:HAS_STATUS]->(s)
MERGE (t)-[:HAS_RESULT]->(r)
MERGE (t)-[:HAS_COMMENT]->(com)
RETURN count(*)
