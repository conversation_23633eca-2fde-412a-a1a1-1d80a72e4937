digraph process_flow {
    rankdir=LR;
    node [shape=box, style=rounded];
    
    start [label="Start", shape=ellipse, style=filled, fillcolor=lightgreen];
    input [label="Get User Input"];
    validate [label="Validate Data?", shape=diamond, style=filled, fillcolor=lightyellow];
    process [label="Process Request"];
    save [label="Save to Database"];
    notify [label="Send Notification"];
    error [label="Show Error", style=filled, fillcolor=lightcoral];
    end [label="End", shape=ellipse, style=filled, fillcolor=lightgray];
    
    start -> input;
    input -> validate;
    validate -> process [label="Valid"];
    validate -> error [label="Invalid"];
    process -> save;
    save -> notify;
    notify -> end;
    error -> input [label="Retry"];
}
