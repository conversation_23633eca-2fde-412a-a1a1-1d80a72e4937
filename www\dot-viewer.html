<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graph Viewer | Ctx | Gemini-CLI Dashboard</title>
    <link rel="stylesheet" href="https://unpkg.com/open-props@1.7.4/open-props.min.css">
    <link rel="stylesheet" href="https://unpkg.com/open-props@1.7.4/normalize.min.css">
    <link rel="stylesheet" href="style.css">
    
    <!-- GraphViz WASM Library -->
    <script src="https://unpkg.com/@hpcc-js/wasm@2.13.0/dist/graphviz.umd.js"></script>
    
    <style>
      /* Always show vertical scrollbar to prevent layout jank */
      html {
        overflow-y: scroll;
      }
      
      /* Viewer specific styles */
      .viewer-container {
        max-width: 1600px;
        margin: 0 auto;
        padding: var(--size-6);
        opacity: 0;
        animation: fadeIn 0.5s ease forwards;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      .viewer-header {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius);
        padding: var(--size-4);
        margin-bottom: var(--size-6);
        box-shadow: var(--shadow);
      }
      
      .graph-title {
        font-size: var(--font-size-5);
        font-weight: var(--font-weight-7);
        margin: 0 0 var(--size-2) 0;
        color: var(--text-color);
      }
      
      .graph-description {
        font-size: var(--font-size-2);
        color: var(--text-light);
        margin: 0 0 var(--size-3) 0;
        line-height: var(--font-lineheight-3);
      }
      
      .graph-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: var(--size-3);
        margin-bottom: var(--size-4);
      }
      
      .meta-info {
        display: flex;
        gap: var(--size-4);
        align-items: center;
        flex-wrap: wrap;
      }
      
      .meta-item {
        display: flex;
        align-items: center;
        gap: var(--size-1);
        font-size: var(--font-size-1);
        color: var(--text-light);
      }
      
      .graph-engine {
        background: var(--blue-2);
        color: var(--blue-9);
        padding: var(--size-1) var(--size-2);
        border-radius: var(--radius-1);
        font-weight: var(--font-weight-5);
      }
      
      .graph-tags {
        display: flex;
        flex-wrap: wrap;
        gap: var(--size-1);
      }
      
      .tag {
        background: var(--gray-2);
        color: var(--gray-8);
        padding: var(--size-1) var(--size-2);
        border-radius: var(--radius-1);
        font-size: var(--font-size-0);
        font-weight: var(--font-weight-5);
      }
      
      .viewer-actions {
        display: flex;
        gap: var(--size-3);
        flex-wrap: wrap;
      }
      
      .btn {
        padding: var(--size-2) var(--size-4);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-1);
        background: var(--gray-1);
        color: var(--gray-9);
        font-size: var(--font-size-2);
        font-weight: var(--font-weight-5);
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--size-1);
      }
      
      .btn:hover {
        background: var(--gray-2);
        border-color: var(--gray-6);
      }
      
      .btn-primary {
        background: var(--blue-7);
        color: var(--gray-0);
        border-color: var(--blue-8);
      }
      
      .btn-primary:hover {
        background: var(--blue-8);
        border-color: var(--blue-9);
      }
      
      .graph-display {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius);
        padding: var(--size-4);
        box-shadow: var(--shadow);
        margin-bottom: var(--size-6);
      }
      
      .display-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--size-4);
        padding-bottom: var(--size-3);
        border-bottom: 1px solid var(--border-color);
      }
      
      .zoom-controls {
        display: flex;
        gap: var(--size-2);
        align-items: center;
      }
      
      .zoom-btn {
        padding: var(--size-1) var(--size-2);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-1);
        background: var(--gray-1);
        color: var(--gray-9);
        font-size: var(--font-size-1);
        cursor: pointer;
        transition: all 0.2s ease;
      }
      
      .zoom-btn:hover {
        background: var(--gray-2);
      }
      
      .zoom-level {
        font-size: var(--font-size-1);
        color: var(--text-light);
        min-width: 60px;
        text-align: center;
      }
      
      .graph-container {
        width: 100%;
        min-height: 400px;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-1);
        background: var(--gray-0);
        overflow: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      }
      
      .graph-svg {
        max-width: 100%;
        height: auto;
        transition: transform 0.2s ease;
        cursor: grab;
      }
      
      .graph-svg:active {
        cursor: grabbing;
      }
      
      .loading-placeholder {
        color: var(--text-light);
        font-style: italic;
        text-align: center;
        padding: var(--size-8);
      }
      
      .error-message {
        background: var(--red-1);
        border: 1px solid var(--red-4);
        border-radius: var(--radius-1);
        padding: var(--size-4);
        color: var(--red-9);
        text-align: center;
      }
      
      .code-section {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius);
        padding: var(--size-4);
        box-shadow: var(--shadow);
      }
      
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--size-4);
        padding-bottom: var(--size-3);
        border-bottom: 1px solid var(--border-color);
      }
      
      .section-title {
        font-size: var(--font-size-3);
        font-weight: var(--font-weight-6);
        margin: 0;
        color: var(--text-color);
      }
      
      .code-display {
        background: var(--gray-0);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-1);
        padding: var(--size-3);
        font-family: 'Courier New', monospace;
        font-size: var(--font-size-1);
        line-height: var(--font-lineheight-3);
        color: var(--gray-9);
        white-space: pre-wrap;
        overflow-x: auto;
        max-height: 300px;
        overflow-y: auto;
      }
      
      /* Dark mode support */
      @media (prefers-color-scheme: dark) {
        .graph-container {
          background: var(--gray-8);
        }
        
        .code-display {
          background: var(--gray-8);
          color: var(--gray-1);
        }
        
        .graph-engine {
          background: var(--blue-8);
          color: var(--blue-1);
        }
        
        .tag {
          background: var(--gray-7);
          color: var(--gray-2);
        }
      }
      
      /* Responsive design */
      @media (max-width: 768px) {
        .graph-meta {
          flex-direction: column;
          align-items: stretch;
        }
        
        .viewer-actions {
          justify-content: center;
        }
        
        .display-controls {
          flex-direction: column;
          gap: var(--size-3);
          align-items: stretch;
        }
        
        .zoom-controls {
          justify-content: center;
        }
      }

      /* Dark theme support */
      @media (prefers-color-scheme: dark) {
        body {
          background: var(--gray-12);
          color: var(--gray-0);
        }

        .btn {
          background: var(--gray-10);
          color: var(--gray-0);
          border-color: var(--gray-8);
        }

        .btn:hover {
          background: var(--gray-9);
        }

        .btn.primary {
          background: var(--blue-9);
          color: var(--gray-0);
        }

        .btn.primary:hover {
          background: var(--blue-8);
        }

        .graph-viewer {
          background: var(--gray-11);
          border-color: var(--gray-8);
        }

        .graph-info {
          background: var(--gray-11);
          border-color: var(--gray-8);
        }

        .source-code {
          background: var(--gray-12);
          color: var(--gray-2);
          border-color: var(--gray-8);
        }

        .loading-state,
        .error-state {
          color: var(--gray-5);
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="header-content">
        <div class="header-left">
          <h1 class="site-title">
            <a href="index.html">Ctx | Gemini-CLI Dashboard</a>
          </h1>
          <span class="page-subtitle">Graph Viewer</span>
        </div>
        <nav class="header-nav">
          <a href="index.html" class="nav-link">📄 Documents</a>
          <a href="dot-gallery.html" class="nav-link">📊 Graph Gallery</a>
          <a href="dot-editor.html" class="nav-link">✏️ DOT Editor</a>
        </nav>
      </div>
    </header>

    <main class="viewer-container">
      <!-- Graph Header -->
      <div class="viewer-header">
        <h2 id="graph-title" class="graph-title">Loading...</h2>
        <p id="graph-description" class="graph-description"></p>
        
        <div class="graph-meta">
          <div class="meta-info">
            <div class="meta-item">
              <span>Engine:</span>
              <span id="graph-engine" class="graph-engine">dot</span>
            </div>
            <div class="meta-item">
              <span>Created:</span>
              <span id="graph-created">-</span>
            </div>
            <div class="meta-item">
              <span>Modified:</span>
              <span id="graph-modified">-</span>
            </div>
          </div>
          <div id="graph-tags" class="graph-tags">
            <!-- Tags will be populated by JavaScript -->
          </div>
        </div>
        
        <div class="viewer-actions">
          <a href="dot-gallery.html" class="btn">← Back to Gallery</a>
          <button id="edit-btn" class="btn btn-primary">✏️ Edit Graph</button>
          <button id="export-svg-btn" class="btn">📥 Export SVG</button>
          <button id="export-png-btn" class="btn">📥 Export PNG</button>
          <button id="fullscreen-btn" class="btn">🔍 Fullscreen</button>
        </div>
      </div>

      <!-- Graph Display -->
      <div class="graph-display">
        <div class="display-controls">
          <h3 class="section-title">Graph Visualization</h3>
          <div class="zoom-controls">
            <button id="zoom-out-btn" class="zoom-btn">−</button>
            <span id="zoom-level" class="zoom-level">100%</span>
            <button id="zoom-in-btn" class="zoom-btn">+</button>
            <button id="zoom-fit-btn" class="zoom-btn">Fit</button>
            <button id="zoom-reset-btn" class="zoom-btn">Reset</button>
          </div>
        </div>
        
        <div id="graph-container" class="graph-container">
          <div class="loading-placeholder">Loading graph...</div>
        </div>
      </div>

      <!-- DOT Code Display -->
      <div class="code-section">
        <div class="section-header">
          <h3 class="section-title">DOT Source Code</h3>
          <button id="copy-code-btn" class="btn">📋 Copy Code</button>
        </div>
        <div id="code-display" class="code-display">
          <!-- DOT code will be populated by JavaScript -->
        </div>
      </div>
    </main>

    <footer id="page-footer" style="opacity: 1;">
      <p>Maintained by Ctx. Served by http-server.</p>
    </footer>

    <script src="dot-viewer.js"></script>
  </body>
</html>
