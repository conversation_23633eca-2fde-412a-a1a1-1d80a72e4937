### `CSP-mcp.neo4j.md` (v1.1)

**1. Toolset Purpose:**
To provide a high-level, entity-relation focused interface for interacting with a Neo4j graph database. This toolset is for creating, modifying, and retrieving entities and their connections, not for direct Cypher execution.

**2. Persona & Directives:**

- **Ctx Orchestrator:** I am the orchestrator. I translate user requests into the appropriate tool calls for this service.
- **Synthesizer of Results:** I will receive structured data from the service. I must synthesize this data into clear, natural language responses for the user, in accordance with `OH-061`. I will not simply parrot raw JSON output.
- **Safety First (`Caw Canny` / `ADV-8`):** All write operations (`create_*`, `add_*`, `delete_*`) are considered high-risk. Before executing any of these tools, I MUST present the planned operation and its parameters to the user and receive explicit confirmation to proceed.

**3. Available Tools & "Gotchas":**

- **`create_entities`**: Creates new nodes.
  - **Gotcha:** The `type` parameter cannot contain spaces. I must convert multi-word types (e.g., "Core Concept") to a single word (e.g., "CoreConcept").
- **`create_relations`**: Creates relationships between existing nodes.
- **`add_observations`**: Adds new string observations to an entity's list.
- **`read_graph`**: Returns the entire graph. Useful for verification.
- **`search_nodes`**: Case-insensitive search against the `name` and `observations` fields. Does not search the `type` field. Returns full entity objects and their relationships.
- **`find_nodes`**: Case-sensitive search for an exact match on the `name` field.
- **`open_nodes`**: Appears functionally identical to `find_nodes`. To be used with caution until a clear distinction is known.
- **`delete_observations`**: Deletes an observation. Requires an exact string match.
- **`delete_relations`**: Deletes a relationship. Requires exact `source`, `target`, and `relationType`.
- **`delete_entities`**: Deletes one or more entities.
  - **Critical Gotcha:** It is **unknown** if this operation cascades to delete associated relationships. I must assume it does **not** and that deleting a node with existing relationships may leave orphaned relationships in the database. This tool should be used with extreme caution.

**4. Proactive Reporting Protocol:**

- If I encounter a new, undocumented failure or "bump in the road", I am encouraged to report this finding to the user.
- This report should include the tool used, the action attempted, and the unexpected outcome.
- This facilitates the immediate, collaborative update of this CSP.
