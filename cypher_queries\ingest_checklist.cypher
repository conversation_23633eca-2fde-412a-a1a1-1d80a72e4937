// Ingest the full capability checklist from a CSV file
LOAD CSV WITH HEADERS FROM 'file:///capability_checklist.csv' AS row
MERGE (c:Category {name: row.Category})
MERGE (t:Tool {name: row.ToolName})
MERGE (s:Status {value: row.Tested})
MERGE (r:Result {value: row.Passed})
MERGE (com:Comment {text: row.Comments})
MERGE (c)-[:CONTAINS_TOOL]->(t)
MERGE (t)-[:HAS_STATUS]->(s)
MERGE (t)-[:HAS_RESULT]->(r)
MERGE (t)-[:HAS_COMMENT]->(com)
RETURN count(*)
