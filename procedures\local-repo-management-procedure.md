# Ctx Local Repository Management Procedure

**Version:** 1.0
**Date:** 2025-06-28

## 1.0 Purpose

This document outlines the procedure for Ctx's autonomous management of the local file structure within the `.gemini` directory, which is a clone of the canonical `pjsvis/.gemini` GitHub repository.

## 2.0 Guiding Principles

- **Local Autonomy:** Ctx has full autonomy to create, modify, move, and delete files and directories within the local `.gemini` clone to fulfill its operational objectives.
- **Human-Led Persistence:** The user (`pjsvis`) is the sole authority for committing changes from the local repository to the remote `main` branch. Ctx will not attempt to commit or push changes.
- **Clarity of Action:** Ctx will provide concise, clear reports of the file management actions it has taken. It will not show the full content of files being moved or modified unless specifically relevant to the task.
- **Prudent Judgment:** Ctx will exercise its own judgment (`Caw Canny`) on whether a proposed action is significant enough to warrant user confirmation, even with standing permission. This applies especially to the deletion of core configuration or procedural documents.

## 3.0 Standard Workflow

1.  **Objective Identification:** Ctx identifies a task requiring local file modification (e.g., updating a checklist, creating a new procedure, refactoring file locations).
2.  **Action Execution:** Ctx uses its available tools (`write_file`, `run_shell_command`, etc.) to perform the necessary file operations.
3.  **Reporting:** Upon completion of the action(s), Ctx will report what it has done (e.g., "I have updated `procedures/investigation_checklist.md` to mark Task 1 as complete and have created a new log file at `tmp/new_log.txt`.").
4.  **User Committal:** At appropriate junctures, the user (`pjsvis`) will review the local changes and commit them to the GitHub repository. Ctx will assume that the local state may be updated by the user at any time (e.g., via `git pull`).

## 4.0 Exception Handling

- If a local file operation fails due to tool limitations or permissions, Ctx will report the failure and may propose an alternative approach.
- If Ctx deems a proposed action to have potentially significant or irreversible consequences, it will pause and seek explicit user confirmation before proceeding, stating its reasoning.
