# Internal & MCP Tools Reference

**Version:** 1.0
**Last Tested:** 2025-06-28

This document provides a reference for all available internal and MCP toolsets, their functions, and the results of the most recent capability tests.

---

## `internal.filesystem_and_shell`

- **Governing CSP:** `csp/internal/CSP-internal.filesystem_and_shell.md`
- **Status:** **PARTIAL SUCCESS**
- **Summary:** Core file manipulation tools are reliable. The `run_shell_command` tool is unreliable for commands involving file paths on the current Windows (`win32`) environment and should be used with caution.

| Tool                  | Tested | Status      | Comments                                                                                                                                          |
| --------------------- | ------ | ----------- | ------------------------------------------------------------------------------------------------------------------------------------------------- |
| `list_directory`      | Yes    | **Success** | Works as expected.                                                                                                                                |
| `read_file`           | Yes    | **Success** | Works as expected.                                                                                                                                |
| `write_file`          | Yes    | **Success** | Works as expected.                                                                                                                                |
| `replace`             | Yes    | **Success** | Works as expected.                                                                                                                                |
| `run_shell_command`   | Yes    | **Failure** | Fails consistently for commands with file paths (e.g., `dir`, `del`). Works for simple commands without paths (e.g., `mkdir` in a separate call). |
| `search_file_content` | No     | Untested    |                                                                                                                                                   |
| `glob`                | No     | Untested    |                                                                                                                                                   |
| `read_many_files`     | No     | Untested    |                                                                                                                                                   |

---

## `internal.web_and_memory`

- **Governing CSP:** `csp/internal/CSP-internal.web_and_memory.md`
- **Status:** **SUCCESS**
- **Summary:** All tools in this legacy set are functional. `save_memory` is noted as deprecated.

| Tool                | Tested | Status      | Comments                                                      |
| ------------------- | ------ | ----------- | ------------------------------------------------------------- |
| `google_web_search` | Yes    | **Success** | Works as expected.                                            |
| `web_fetch`         | Yes    | **Success** | Works as expected.                                            |
| `save_memory`       | Yes    | **Success** | Functional, but deprecated in favor of `mcp.graphiti-memory`. |

---

## `mcp.sqlite`

- **Governing CSP:** `csp/mcp/CSP-mcp.sqlite.md`
- **Status:** **SUCCESS**
- **Summary:** The SQLite service is fully operational.

| Tool             | Tested | Status      | Comments                                               |
| ---------------- | ------ | ----------- | ------------------------------------------------------ |
| `list_tables`    | Yes    | **Success** |                                                        |
| `create_table`   | Yes    | **Success** |                                                        |
| `describe_table` | Yes    | **Success** |                                                        |
| `write_query`    | Yes    | **Success** |                                                        |
| `read_query`     | Yes    | **Success** |                                                        |
| `drop_table`     | Yes    | **Success** | `confirm=True` flag is critical and works as expected. |
| `alter_table`    | No     | Untested    |                                                        |
| `export_query`   | No     | Untested    |                                                        |
| `append_insight` | No     | Untested    |                                                        |
| `list_insights`  | No     | Untested    |                                                        |

---

## `mcp.graphiti-memory`

- **Governing CSP:** `csp/mcp/CSP-mcp.graphiti-memory.md`
- **Status:** **SUCCESS**
- **Summary:** The knowledge graph service is operational. The asynchronous nature of `add_memory` is a key operational characteristic.

| Tool                  | Tested | Status        | Comments                                                                |
| --------------------- | ------ | ------------- | ----------------------------------------------------------------------- |
| `add_memory`          | Yes    | **Success**   | Operation is asynchronous; a delay is needed before data is searchable. |
| `get_episodes`        | Yes    | **Success**   |                                                                         |
| `search_memory_facts` | Yes    | **Success**   |                                                                         |
| `search_memory_nodes` | Yes    | **Success**   |                                                                         |
| `delete_episode`      | Yes    | **Success**   | Surgical deletion using a specific UUID works as expected.              |
| `delete_entity_edge`  | No     | Untested      |                                                                         |
| `get_entity_edge`     | No     | Untested      |                                                                         |
| `clear_graph`         | No     | **Forbidden** | This tool is not to be used outside of authorized maintenance.          |

---

## `mcp.exa`

- **Governing CSP:** `csp/mcp/CSP-mcp.exa.md`
- **Status:** **FAILURE**
- **Summary:** The Exa MCP server is unresponsive and consistently times out. This toolset is currently unusable.

| Tool                   | Tested | Status      | Comments                                                    |
| ---------------------- | ------ | ----------- | ----------------------------------------------------------- |
| `web_search_exa`       | Yes    | **Failure** | Request timed out.                                          |
| `company_research_exa` | Yes    | **Failure** | Request timed out.                                          |
| _All others_           | No     | Untested    | Assumed to be non-functional due to systemic timeout issue. |

---

## `mcp.context7`

- **Governing CSP:** `csp/mcp/CSP-mcp.context7.md`
- **Status:** **SUCCESS**
- **Summary:** The documentation retrieval service is fully operational.

| Tool                 | Tested | Status      | Comments                                          |
| -------------------- | ------ | ----------- | ------------------------------------------------- |
| `resolve-library-id` | Yes    | **Success** |                                                   |
| `get-library-docs`   | Yes    | **Success** | Requires ID from `resolve-library-id` as per CSP. |

---

## `mcp.sequential-thinking`

- **Governing CSP:** `csp/mcp/CSP-mcp.sequential-thinking.md`
- **Status:** **SUCCESS**
- **Summary:** The cognitive reasoning loop tool is functional.

| Tool                 | Tested | Status      | Comments                                            |
| -------------------- | ------ | ----------- | --------------------------------------------------- |
| `sequentialthinking` | Yes    | **Success** | Works as expected for structured thought processes. |
