class DOTViewer {
    constructor() {
        this.graphviz = null;
        this.currentGraph = null;
        this.currentSVG = null;
        this.zoomLevel = 1;
        this.isDragging = false;
        this.dragStart = { x: 0, y: 0 };
        this.graphOffset = { x: 0, y: 0 };
        
        this.initializeElements();
        this.setupEventListeners();
        this.initializeGraphviz();
        this.loadGraph();
    }

    initializeElements() {
        this.graphTitle = document.getElementById('graph-title');
        this.graphDescription = document.getElementById('graph-description');
        this.graphEngine = document.getElementById('graph-engine');
        this.graphCreated = document.getElementById('graph-created');
        this.graphModified = document.getElementById('graph-modified');
        this.graphTags = document.getElementById('graph-tags');
        this.graphContainer = document.getElementById('graph-container');
        this.codeDisplay = document.getElementById('code-display');
        this.zoomLevel = document.getElementById('zoom-level');
        
        this.editBtn = document.getElementById('edit-btn');
        this.exportSvgBtn = document.getElementById('export-svg-btn');
        this.exportPngBtn = document.getElementById('export-png-btn');
        this.fullscreenBtn = document.getElementById('fullscreen-btn');
        this.copyCodeBtn = document.getElementById('copy-code-btn');
        
        this.zoomInBtn = document.getElementById('zoom-in-btn');
        this.zoomOutBtn = document.getElementById('zoom-out-btn');
        this.zoomFitBtn = document.getElementById('zoom-fit-btn');
        this.zoomResetBtn = document.getElementById('zoom-reset-btn');
    }

    setupEventListeners() {
        this.editBtn.addEventListener('click', () => this.editGraph());
        this.exportSvgBtn.addEventListener('click', () => this.exportSVG());
        this.exportPngBtn.addEventListener('click', () => this.exportPNG());
        this.fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
        this.copyCodeBtn.addEventListener('click', () => this.copyCode());
        
        this.zoomInBtn.addEventListener('click', () => this.zoomIn());
        this.zoomOutBtn.addEventListener('click', () => this.zoomOut());
        this.zoomFitBtn.addEventListener('click', () => this.zoomToFit());
        this.zoomResetBtn.addEventListener('click', () => this.zoomReset());
        
        // Mouse wheel zoom
        this.graphContainer.addEventListener('wheel', (e) => this.handleWheel(e));
        
        // Pan functionality
        this.graphContainer.addEventListener('mousedown', (e) => this.startDrag(e));
        this.graphContainer.addEventListener('mousemove', (e) => this.drag(e));
        this.graphContainer.addEventListener('mouseup', () => this.endDrag());
        this.graphContainer.addEventListener('mouseleave', () => this.endDrag());
    }

    async initializeGraphviz() {
        try {
            this.graphviz = await window['@hpcc-js/wasm'].Graphviz.load();
        } catch (error) {
            console.error('Failed to initialize GraphViz:', error);
            this.showError('Failed to load GraphViz. Please refresh the page.');
        }
    }

    async loadGraph() {
        const urlParams = new URLSearchParams(window.location.search);
        const graphId = urlParams.get('id');
        
        if (!graphId) {
            this.showError('No graph ID specified.');
            return;
        }
        
        try {
            // Try to find graph in manifest
            const manifestResponse = await fetch('graph-manifest.json');
            const manifest = await manifestResponse.json();
            let graph = manifest.graphs.find(g => g.id === graphId);
            
            // If not found in manifest, check localStorage
            if (!graph) {
                const localGraphs = JSON.parse(localStorage.getItem('dotGraphs') || '[]');
                graph = localGraphs.find(g => g.id === graphId);
            }
            
            if (!graph) {
                this.showError('Graph not found.');
                return;
            }
            
            this.currentGraph = graph;
            await this.displayGraph();
            
        } catch (error) {
            console.error('Failed to load graph:', error);
            this.showError('Failed to load graph data.');
        }
    }

    async displayGraph() {
        if (!this.currentGraph) return;
        
        // Update header information
        this.graphTitle.textContent = this.currentGraph.title;
        this.graphDescription.textContent = this.currentGraph.description || 'No description';
        this.graphEngine.textContent = this.currentGraph.engine || 'dot';
        
        if (this.currentGraph.created) {
            this.graphCreated.textContent = new Date(this.currentGraph.created).toLocaleDateString();
        }
        if (this.currentGraph.modified) {
            this.graphModified.textContent = new Date(this.currentGraph.modified).toLocaleDateString();
        }
        
        // Update tags
        this.graphTags.innerHTML = '';
        if (this.currentGraph.tags && this.currentGraph.tags.length > 0) {
            this.currentGraph.tags.forEach(tag => {
                const tagElement = document.createElement('span');
                tagElement.className = 'tag';
                tagElement.textContent = tag;
                this.graphTags.appendChild(tagElement);
            });
        }
        
        // Load and display DOT code
        let dotCode = this.currentGraph.dotCode;
        
        // If no dotCode in graph object, try to load from file
        if (!dotCode && this.currentGraph.fileName) {
            try {
                const response = await fetch(`graphs/${this.currentGraph.fileName}`);
                dotCode = await response.text();
            } catch (error) {
                console.warn(`Could not load DOT file: ${this.currentGraph.fileName}`);
            }
        }
        
        if (!dotCode) {
            this.showError('No DOT code available for this graph.');
            return;
        }
        
        this.codeDisplay.textContent = dotCode;
        
        // Render the graph
        await this.renderGraph(dotCode);
    }

    async renderGraph(dotCode) {
        if (!this.graphviz) {
            this.showError('GraphViz not initialized.');
            return;
        }
        
        try {
            const engine = this.currentGraph.engine || 'dot';
            const svg = this.graphviz.layout(dotCode, "svg", engine);
            
            this.graphContainer.innerHTML = svg;
            this.currentSVG = this.graphContainer.querySelector('svg');
            
            if (this.currentSVG) {
                this.currentSVG.classList.add('graph-svg');
                this.zoomToFit();
            }
            
        } catch (error) {
            console.error('Render error:', error);
            this.showError(`Failed to render graph: ${error.message}`);
        }
    }

    editGraph() {
        if (!this.currentGraph) return;
        
        // Store graph data for editor to pick up
        sessionStorage.setItem('editGraph', JSON.stringify(this.currentGraph));
        window.location.href = 'dot-editor.html';
    }

    exportSVG() {
        if (!this.currentSVG) {
            alert('No graph to export.');
            return;
        }
        
        const svgData = new XMLSerializer().serializeToString(this.currentSVG);
        const blob = new Blob([svgData], { type: 'image/svg+xml' });
        this.downloadBlob(blob, `${this.sanitizeFileName(this.currentGraph.title)}.svg`);
    }

    exportPNG() {
        if (!this.currentSVG) {
            alert('No graph to export.');
            return;
        }
        
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);
            
            canvas.toBlob((blob) => {
                this.downloadBlob(blob, `${this.sanitizeFileName(this.currentGraph.title)}.png`);
            }, 'image/png');
        };
        
        img.onerror = () => {
            alert('Failed to export PNG. Try SVG export instead.');
        };
        
        const svgData = new XMLSerializer().serializeToString(this.currentSVG);
        const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
        const url = URL.createObjectURL(svgBlob);
        img.src = url;
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            this.graphContainer.requestFullscreen();
            this.fullscreenBtn.textContent = '🔍 Exit Fullscreen';
        } else {
            document.exitFullscreen();
            this.fullscreenBtn.textContent = '🔍 Fullscreen';
        }
    }

    async copyCode() {
        try {
            await navigator.clipboard.writeText(this.codeDisplay.textContent);
            this.copyCodeBtn.textContent = '✅ Copied!';
            setTimeout(() => {
                this.copyCodeBtn.textContent = '📋 Copy Code';
            }, 2000);
        } catch (error) {
            console.error('Failed to copy code:', error);
            alert('Failed to copy code to clipboard.');
        }
    }

    // Zoom and pan functionality
    zoomIn() {
        this.setZoom(this.zoomLevel * 1.2);
    }

    zoomOut() {
        this.setZoom(this.zoomLevel * 0.8);
    }

    zoomReset() {
        this.setZoom(1);
    }

    zoomToFit() {
        if (!this.currentSVG) return;
        
        const containerRect = this.graphContainer.getBoundingClientRect();
        const svgRect = this.currentSVG.getBoundingClientRect();
        
        const scaleX = (containerRect.width - 40) / svgRect.width;
        const scaleY = (containerRect.height - 40) / svgRect.height;
        const scale = Math.min(scaleX, scaleY, 1);
        
        this.setZoom(scale);
    }

    setZoom(zoom) {
        this.zoomLevel = Math.max(0.1, Math.min(5, zoom));
        
        if (this.currentSVG) {
            this.currentSVG.style.transform = `scale(${this.zoomLevel})`;
        }
        
        this.zoomLevelDisplay.textContent = `${Math.round(this.zoomLevel * 100)}%`;
    }

    handleWheel(e) {
        e.preventDefault();
        const delta = e.deltaY > 0 ? 0.9 : 1.1;
        this.setZoom(this.zoomLevel * delta);
    }

    startDrag(e) {
        if (!this.currentSVG) return;
        
        this.isDragging = true;
        this.dragStart = { x: e.clientX, y: e.clientY };
        this.graphContainer.style.cursor = 'grabbing';
    }

    drag(e) {
        if (!this.isDragging || !this.currentSVG) return;
        
        const deltaX = e.clientX - this.dragStart.x;
        const deltaY = e.clientY - this.dragStart.y;
        
        this.graphOffset.x += deltaX;
        this.graphOffset.y += deltaY;
        
        this.currentSVG.style.transform = `scale(${this.zoomLevel}) translate(${this.graphOffset.x}px, ${this.graphOffset.y}px)`;
        
        this.dragStart = { x: e.clientX, y: e.clientY };
    }

    endDrag() {
        this.isDragging = false;
        this.graphContainer.style.cursor = 'grab';
    }

    showError(message) {
        this.graphContainer.innerHTML = `
            <div class="error-message">
                <strong>Error:</strong> ${this.escapeHtml(message)}
            </div>
        `;
    }

    downloadBlob(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    sanitizeFileName(name) {
        return name.toLowerCase()
                  .replace(/[^a-z0-9]/g, '-')
                  .replace(/-+/g, '-')
                  .replace(/^-|-$/g, '');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize the viewer when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new DOTViewer();
});
