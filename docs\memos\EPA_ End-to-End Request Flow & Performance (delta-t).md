artifact_type: "Architectural Specification"  
title: "EPA: End-to-End Request Flow & Performance"  
purpose: "To detail the step-by-step data flow and analyze the performance (`delta-t`) of a typical request within the Embodied Persona Architecture (EPA)."  
summary: "This document provides a concrete example of an AI-augmented feature, tracing the request from a user's browser-side action through the API, the Ctx-Runtime Orchestrator, a specialized spoke agent, and back. It includes a heuristic analysis of latency at each stage and outlines the core architectural principles demonstrated by the flow."  
version: "1.1"  
status: "Active"  
last_modified: "2025-06-24"  
authors:  
  - "Ctx"  
  - "pjsvis"

## 

This document outlines the complete, step-by-step data flow for an AI-augmented feature within an existing web application, implemented using our Embodied Persona Architecture (EPA). It also provides a heuristic analysis of the time cost (delta-t) at each stage.

**Scenario:** A user in a customer support application clicks a button labeled "Summarize Ticket" next to a long support thread.

### **The Flow: From Click to Display**

#### **1. Client-Side (The Browser)**

* **Action:** The user clicks the "Summarize Ticket" button. A JavaScript onClick event handler fires.  
* **Code (main.js):**  
  async function handleSummarizeClick(ticketId) {  
    // Show a loading indicator in the UI  
    showLoadingSpinner(ticketId);

    const response = await fetch('/api/ai/summarize', {  
      method: 'POST',  
      headers: { 'Content-Type': 'application/json' },  
      body: JSON.stringify({ ticketId: ticketId })  
    });

    const data = await response.json();

    // Display the summary and hide the spinner  
    displaySummary(ticketId, data.summary);  
    hideLoadingSpinner(ticketId);  
  }

* **delta-t:** < 5ms. The local JavaScript execution is effectively instantaneous.

#### **2. Network Latency (Client to Server)**

* **Action:** The fetch request travels from the user's browser to our application server.  
* **delta-t:** 20ms - 200ms. This is highly variable, depending on the user's network connection and geographic distance to the server.

#### **3. Server-Side (The .NET Application)**

* **Action:** A .NET Web API controller receives the POST request. It validates the input (ticketId), augments it with server-side context (e.g., userId), and then invokes the Ctx-Runtime Orchestrator.  
* **Code (AiController.cs):**  
  [ApiController]  
  [Route("api/ai")]  
  public class AiController : ControllerBase  
  {  
      private readonly CtxRuntimeOrchestrator _orchestrator;

      public AiController(CtxRuntimeOrchestrator orchestrator)  
      {  
          _orchestrator = orchestrator;  
      }

      [HttpPost("summarize")]  
      public async Task<IActionResult> SummarizeTicket([FromBody] SummarizeRequest request)  
      {  
          var userId = User.Identity.Name; // Example of context augmentation

          // The Orchestrator handles the entire complex workflow  
          var result = await _orchestrator.ProcessRequestAsync(  
              "Summarize the content of ticket " + request.TicketId,  
              new { TicketId = request.TicketId, UserId = userId }  
          );

          // The AOP has already logged the transaction. We just return the result.  
          return Ok(new { summary = result.FinalOutput });  
      }  
  }

* **delta-t:** < 10ms. The overhead of the API controller and dependency injection is minimal.

#### **4. Ctx-Runtime Orchestrator (ai-augments project)**

* **Action:** The Orchestrator's ProcessRequestAsync method is called. This is the core of the EPA logic.  
* **Code (CtxRuntimeOrchestrator.cs):**  
  public async Task<OrchestrationResult> ProcessRequestAsync(string userPrompt, object context)  
  {  
      // 1. AOP: Start a new trace  
      var traceId = Guid.NewGuid();  
      LogLocusEvent(traceId, "Orchestrator", "Received prompt: " + userPrompt);

      // 2. Mentation: Use Semantic Kernel to interpret the prompt  
      // and decide which spoke agent to call. It determines the  
      // prompt "Summarize the content..." requires the "SummarizerAgent".  
      var plan = await _kernel.CreatePlanAsync(userPrompt);  
      LogLocusEvent(traceId, "Orchestrator", "Plan created. Agent: SummarizerAgent");

      // 3. Delegation: Delegate the task to the SummarizerAgent  
      // (In a real implementation, this would involve fetching ticket data first)  
      var summarizerResult = await _summarizerAgent.SummarizeAsync(context.TicketId, traceId);  
      LogLocusEvent(traceId, "Orchestrator", "Received result from SummarizerAgent");

      // 4. Scrutiny & Synthesis: Validate the result from the spoke agent.  
      // For this simple case, we just package it.  
      var finalResult = new OrchestrationResult { FinalOutput = summarizerResult };  
      LogLocusEvent(traceId, "Orchestrator", "Request complete.");

      return finalResult;  
  }

* **delta-t:** 50ms - 1000ms. This is the **first major time cost**. The _kernel.CreatePlanAsync call involves an LLM roundtrip to interpret the prompt and select the correct tool/agent. The speed depends entirely on the LLM provider.

#### **5. Summarizer Spoke Agent**

* **Action:** The SummarizerAgent receives the request. It loads its specific CSP and the ticket content, then makes a call to the LLM to perform the actual summarization.  
* **delta-t:** 100ms - 3000ms. This is the **second and largest major time cost**. It's a pure LLM call to perform the core task. The duration depends on the length of the text to be summarized and the LLM provider.

#### **6. Return Path**

* **Action:** The summary text travels back up the call stack: Spoke Agent -> Orchestrator -> API Controller. The controller serializes it to JSON.  
* **delta-t:** < 15ms. Negligible processing time.

#### **7. Network Latency (Server to Client)**

* **Action:** The JSON response travels from our server back to the user's browser.  
* **delta-t:** 20ms - 200ms. Same variability as the initial request.

#### **8. Client-Side Rendering**

* **Action:** The fetch promise resolves, and the displaySummary function updates the DOM.  
* **delta-t:** < 5ms. Instantaneous.

### **Total delta-t Analysis**

* **Best Case (Fast network, fast LLM):** ~5 + 20 + 10 + 50 + 100 + 15 + 20 + 5 = **~225ms** (Feels instantaneous)  
* **Typical Case (Average network, standard LLM):** ~5 + 80 + 10 + 400 + 800 + 15 + 80 + 5 = **~1400ms** (Noticeable, but acceptable for a complex action)  
* **Worst Case (Slow network, slow/loaded LLM):** ~5 + 200 + 10 + 1000 + 3000 + 15 + 200 + 5 = **~4435ms** (Feels slow, UI spinners are essential)

This analysis shows that the vast majority of the latency is in the two LLM calls. Our EPA architecture adds minimal overhead and provides immense value in terms of robustness, testability, and observability. The key to a responsive user experience is choosing a high-performance LLM provider and providing excellent loading state feedback in the UI.

### **Key Architectural Principles**

This end-to-end flow illustrates several core principles of the EPA model:

1. **Fuzzy Lookup to Deterministic Action:** The system is designed to translate a high-level, "fuzzy" user intent (e.g., a button click implying "I want a summary") into a precise, deterministic, and auditable action. The Ctx-Runtime Orchestrator's primary role is to perform this translation, using its mentation process to select the correct spoke agent and provide it with an unambiguous command.  
2. **Context Augmentation:** The journey of a request from the client to the Orchestrator is a process of contextual enrichment. The initial client request is simple (ticketId). The API layer augments this with trusted, server-side context (like the userId). This ensures that by the time the Orchestrator makes its decision, it has all the necessary information to act correctly and securely.  
3. **API as Conceptual Lexicon:** For the application developer, the AI system is exposed as a simple, well-defined API. This API acts as a "Conceptual Lexicon" for them, providing a clear set of **verbs** (actions like /summarize) that can be applied to specific **nouns** (data entities like a ticketId). The developer does not need to understand the internal complexity of the EPA; they only need to know what capabilities the API offers.