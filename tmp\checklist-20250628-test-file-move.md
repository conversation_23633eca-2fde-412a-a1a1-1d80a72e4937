# Checklist: Move test.md

**Status:** COMPLETE

This checklist outlines the shared process for moving the test file. Due to my current tool limitations, this process requires your direct assistance for filesystem manipulation.

- [X] **1. User Action: Create Directory**
  - Please create the following directory: `C:\Users\<USER>\.gemini\docs\testing\`

- [X] **2. Ctx Action: Read Source File**
  - I will read the content of `C:\Users\<USER>\.gemini\cda-matrix-ref\test.md`.

- [X] **3. Ctx Action: Write Destination File**
  - I will write the content to `C:\Users\<USER>\.gemini\docs\testing\test.md`.

- [X] **4. User Action: Delete Original File**
  - Please delete the original file: `C:\Users\<USER>\.gemini\cda-matrix-ref\test.md`.

- [X] **5. Ctx Action: Verify Final State**
  - I will verify that the original file is gone and the new file exists in the correct location.

