{"graphs": [{"id": "example-network-topology", "fileName": "network-topology.dot", "title": "Network Topology", "description": "Server infrastructure diagram showing load balancer and database connections", "created": "2025-06-29T23:55:00Z", "modified": "2025-06-29T23:55:00Z", "tags": ["network", "infrastructure", "example"], "engine": "dot", "toBeArchived": false}, {"id": "example-process-flow", "fileName": "process-flow.dot", "title": "Process Flow", "description": "Simple decision-based process flow diagram", "created": "2025-06-29T23:56:00Z", "modified": "2025-06-29T23:56:00Z", "tags": ["process", "flow", "decision", "example"], "engine": "dot", "toBeArchived": false}]}