version: '3.8'

services:
  neo4j:
    image: neo4j:latest
    ports:
      - "7474:7474"
      - "7687:7687"
    # This mounts a local directory into the container for data persistence.
    # The data will be stored in a 'data' folder in the same directory 
    # where you run 'docker-compose up'.
    volumes:
      - ./data:/data
      - ./logs:/logs
    environment:
      # This tells Neo4j to read authentication details from the secret file.
      - NEO4J_AUTH_FILE=/run/secrets/neo4j_password
    secrets:
      - neo4j_password

secrets:
  neo4j_password:
    # This tells Docker Compose to use the local file as the secret.
    file: ./neo4j_password.txt
